# Zurno AI Booking System - User Manual & Testing Guide

## 🎯 Overview

The Zurno AI Booking System is an enhanced voice-based appointment booking system that integrates with Twilio for phone calls and uses advanced AI capabilities for natural language processing, intent recognition, and automated appointment booking.

## 🏗️ System Architecture

### Core Components

1. **Enhanced AI Receptionist Service** (`app/services/zurno_ai_receptionist_service.ts`)
   - Main service handling voice conversations
   - Integrates with all AI sub-services
   - Maintains Twilio voice response compatibility

2. **AI Sub-Services** (`app/services/ai/`)
   - `BookingNLPService` - Natural language processing and intent extraction
   - `SessionManagementService` - Redis-based conversation state management
   - `IntentHandlerService` - Intent processing and routing
   - `StoreContextService` - Store information and services loading
   - `CustomerContextService` - Customer history and preferences
   - `AppointmentBookingService` - Appointment creation and validation
   - `SchedulingService` - Availability checking and optimization
   - `FallbackDialogService` - Error handling and recovery

3. **Controller** (`app/controllers/app/ai_receptionists_controller.ts`)
   - Handles Twilio webhook requests
   - Manages call flow and responses

## 🔧 API Endpoints

### Base URL: `/v1/receptions`

#### 1. Welcome Endpoint
- **URL**: `POST /v1/receptions/`
- **Purpose**: Initial call handler (Twilio webhook)
- **Parameters**:
  - `CallSid`: Twilio call identifier
  - `From`: Caller's phone number
  - `To`: Store's phone number

#### 2. Process Flow Endpoint
- **URL**: `POST /v1/receptions/process-flow`
- **Purpose**: Processes voice input and generates AI responses
- **Parameters**:
  - `CallSid`: Twilio call identifier
  - `RecordingUrl`: URL to voice recording

#### 3. Process Reception Endpoint
- **URL**: `POST /v1/receptions/process-reception/:id`
- **Purpose**: Handles asynchronous AI processing results
- **Parameters**:
  - `id`: Message ID for processing

## 🧪 Testing the System

### Prerequisites

1. **Environment Variables**:
   ```env
   OPENAI_API_KEY=your_openai_api_key
   REDIS_HOST=localhost
   REDIS_PORT=6379
   REDIS_PASSWORD=your_redis_password (optional)
   REDIS_DB=0
   SUPPORT_PHONE_NUMBER=+**********
   ```

2. **Database Setup**:
   - Ensure all migrations are run
   - Have at least one store with phone number configured
   - Store should have services configured

3. **Dependencies**:
   - Redis server running
   - OpenAI API access
   - Twilio account (for voice testing)

### Test Scenarios

#### Scenario 1: Basic Appointment Booking
**Test Flow**:
1. Call store number
2. Say: "I'd like to book an appointment"
3. AI should ask for service preference
4. Say: "Manicure"
5. AI should ask for preferred date/time
6. Say: "Tomorrow at 2 PM"
7. AI should confirm availability and book

**Expected Behavior**:
- Natural conversation flow
- Service recognition
- Date/time parsing
- Availability checking
- Appointment creation

#### Scenario 2: Service Information Request
**Test Flow**:
1. Call store number
2. Say: "What services do you offer?"
3. AI should list available services with prices

#### Scenario 3: Store Hours Inquiry
**Test Flow**:
1. Call store number
2. Say: "What are your hours?"
3. AI should provide store operating hours

#### Scenario 4: Error Recovery
**Test Flow**:
1. Call store number
2. Say something unclear: "Uhm, I want, like, you know..."
3. AI should ask for clarification
4. Provide clear request
5. AI should process correctly

### Manual Testing with cURL

#### Test Store Context Loading
```bash
# This tests if the system can find and load store information
curl -X POST http://localhost:3333/v1/receptions/ \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "CallSid=test123&From=+**********&To=+1987654321"
```

#### Test Voice Processing (Mock)
```bash
# This simulates voice processing
curl -X POST http://localhost:3333/v1/receptions/process-flow \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "CallSid=test123&RecordingUrl=https://example.com/recording"
```

### Database Verification

#### Check Call Records
```sql
SELECT * FROM zn_calls ORDER BY created_at DESC LIMIT 10;
```

#### Check Call Messages
```sql
SELECT cm.*, c.from_number, c.to_number 
FROM zn_call_messages cm 
JOIN zn_calls c ON cm.call_id = c.id 
ORDER BY cm.created_at DESC LIMIT 10;
```

#### Check Appointments Created
```sql
SELECT a.*, s.name as store_name, u.email as customer_email
FROM zn_appointments a
LEFT JOIN zn_stores s ON a.store_id = s.id
LEFT JOIN zn_users u ON a.customer_id = u.id
ORDER BY a.created_at DESC LIMIT 10;
```

## 🔍 Troubleshooting

### Common Issues

1. **Store Not Found Error**
   - **Symptom**: "Sorry, we could not identify the store"
   - **Solution**: Ensure store has phone number matching the `To` parameter
   - **Check**: `SELECT * FROM zn_stores WHERE phone LIKE '%[number]%'`

2. **OpenAI API Errors**
   - **Symptom**: Generic error responses
   - **Solution**: Check OpenAI API key and quota
   - **Logs**: Look for "OpenAI API error" in console

3. **Redis Connection Issues**
   - **Symptom**: Session management failures
   - **Solution**: Verify Redis server is running and accessible
   - **Test**: `redis-cli ping`

4. **Voice Transcription Failures**
   - **Symptom**: "I'm having trouble understanding"
   - **Solution**: Check Twilio recording URL accessibility
   - **Debug**: Log recording URLs and test manual download

### Debug Mode

Enable detailed logging by setting:
```env
LOG_LEVEL=debug
```

This will provide detailed information about:
- Intent extraction results
- Entity recognition
- Session state changes
- API calls and responses

## 📊 Monitoring & Analytics

### Key Metrics to Monitor

1. **Call Volume**: Number of incoming calls
2. **Intent Recognition Accuracy**: Successful intent classifications
3. **Booking Conversion Rate**: Calls that result in appointments
4. **Error Rate**: Failed processing attempts
5. **Response Time**: AI processing duration

### Log Analysis

Important log patterns to watch:
- `Enhanced AI answer:` - Successful AI responses
- `Fallback AI answer:` - When enhanced AI fails
- `Intent extraction error:` - NLP processing issues
- `Booking validation failed:` - Appointment creation problems

## 🚀 Production Deployment

### Performance Considerations

1. **Redis Configuration**:
   - Use Redis cluster for high availability
   - Set appropriate memory limits
   - Configure persistence if needed

2. **OpenAI Rate Limits**:
   - Monitor API usage
   - Implement request queuing if needed
   - Consider caching common responses

3. **Database Optimization**:
   - Index frequently queried fields
   - Monitor appointment table growth
   - Regular cleanup of old call records

### Security

1. **API Keys**: Store securely in environment variables
2. **Phone Number Validation**: Implement caller verification
3. **Rate Limiting**: Prevent abuse of voice endpoints
4. **Data Privacy**: Ensure compliance with regulations

## 📞 Support

For technical issues or questions:
1. Check logs for error details
2. Verify all prerequisites are met
3. Test with simple scenarios first
4. Contact development team with specific error messages and reproduction steps
