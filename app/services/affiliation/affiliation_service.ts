import { EApprovalStatus } from "#constants/approval_status"
import { ACTION, RESOURCE } from "#constants/authorization"
import { NOTIFICATION_TYPE } from "#constants/notification"
import AdminReceiveRegistrationNotification from "#mails/affiliation/admin_registration_notification"
import PayoutNotification from "#mails/affiliation/payout_notification"
import RefCodeAssignedNotification from "#mails/affiliation/refcode_assigned_notification"
import RegistrationApprovedNotification from "#mails/affiliation/registration_approved_notification"
import RegistrationConfirmationNotification from "#mails/affiliation/registration_confirmation_notification"
import RegistrationReapplyNotification from "#mails/affiliation/registration_reapply_notification"
import RegistrationRejectNotification from "#mails/affiliation/registration_reject_notification"
import TierChangedNotification from "#mails/affiliation/tier_changed_notification"
import Notification from "#models/notification"
import ZnAffiliate from "#models/zn_affiliate"
import ZnAffiliateCommission from "#models/zn_affiliate_commission"
import ZnAffiliateCommissionPayment from "#models/zn_affiliate_commission_payment"
import ZnAffiliateSocialPage from "#models/zn_affiliate_social_page"
import ZnAffiliateTier from "#models/zn_affiliate_tier"
import ZnOrder from "#models/zn_order"
import ZnPaymentMethod from "#models/zn_payment_method"
import ZnUser from "#models/zn_user"
import { NotificationService } from "#services/notification_service"
import env from "#start/env"
import { approvalRegistrationValidator } from "#validators/affiliate"
import logger from "@adonisjs/core/services/logger"
import mail from "@adonisjs/mail/services/main"
import { addDays, endOfMonth, startOfMonth } from "date-fns"
import { DateTime } from "luxon"
import { AdminNotificationService } from "../../../admin/services/notification/admin_notification_service.js"
import { AffiliationCommissionService } from "./affiliation_commission_service.js"
import { AffiliateRefCodeService } from "./affiliation_refcode_service.js"
import { AffiliateTierService } from "./affiliation_tier_service.js"

export interface IAffiliateRegistrationData {
  userId: string,
  socialPages: string[]
}

export interface IDiscountCodeBasicInput {
  title: string,
  code: string,
  startsAt: DateTime,
  endsAt?: DateTime,
  customerGets: IDiscountCustomerGetsInput,
  customerSelection: IDiscountCustomerSelectionInput
}

export interface IDiscountCustomerGetsInput {
  value: IDiscountCodeCustomerGetValue,
  items: IDiscountCodeItemsInput
}

export interface IDiscountCodeCustomerGetValue {
  percentage: number
}

export interface IDiscountCodeItemsInput {
  all: boolean
}

export interface IDiscountCustomerSelectionInput {
  all: boolean
}

export interface IDateRange {
  from: Date | null,
  to: Date | null
}

export interface IAffiliateStatistic {
  totalSoldItems: number,
  totalRevenue: number,
  refCodeUsed: number,
  estimatedCommissionsAmount: number
}

enum ETierAction {
  UPGRADE = 'upgrade',
  DOWNGRADE = 'downgrade'
}

export class AffiliationService {
  private tierService: AffiliateTierService;
  private refCodeService: AffiliateRefCodeService;
  private commissionService: AffiliationCommissionService;
  private adminNotificationService: AdminNotificationService;

  constructor() {
    this.tierService = new AffiliateTierService();
    this.refCodeService = new AffiliateRefCodeService();
    this.commissionService = new AffiliationCommissionService();
    this.adminNotificationService = new AdminNotificationService();
  }

  async getAffiliate(affiliateId: string) {
    return await ZnAffiliate
      .query()
      .where('id', affiliateId)
      .preload('user')
      .preload('affiliateTier')
      .preload('commissions', (query) => {
        query.orderBy('createdAt', 'desc')
      })
      .preload('commissionPayments', (query) => {
        query
          .preload('paymentMethod')
          .orderBy('createdAt', 'desc')
      })
      .preload('socialPages')
      .preload('refCode')
      .preload('paymentMethods', (query) => {
        query
          .preload('paypalDetail')
          .preload('achTransferDetail')
          .preload('otherPaymentDetail')
      })
      .first();
  }

  async getStatistics(affiliateId: string, range: IDateRange): Promise<IAffiliateStatistic> {
    const query = ZnAffiliateCommission.query()
      .preload('commissionDetails', (query) => {
        query.preload('orderDetail');
      })
      .where('affiliateId', affiliateId);

    if (range.from) {
      query.where('createdAt', '>=', range.from);
    }

    if (range.to) {
      query.where('createdAt', '<', addDays(range.to, 1));
    }

    if (!range.from && !range.to) {
      query.where('createdAt', '>=', startOfMonth(new Date()));
      query.where('createdAt', '<', addDays(endOfMonth(new Date()), 1));
    }

    const commissions = await query;

    let totalSoldItems = 0;
    let totalRevenue = 0;
    let estimatedCommissionsAmount = 0;
    for (const commission of commissions) {
      if (commission.status === EApprovalStatus.APPROVED) {
        for (const commissionDetail of commission.commissionDetails) {
          if (commissionDetail.orderDetail.currentQuantity > 0 && commissionDetail.commissionRate > 0) {
            totalSoldItems += commissionDetail.orderDetail.currentQuantity;
            totalRevenue += commissionDetail.orderDetail.price * commissionDetail.orderDetail.currentQuantity - commissionDetail.orderDetail.discount;
          }
        }
      }
      if (commission.status !== EApprovalStatus.REJECTED) {
        estimatedCommissionsAmount += commission.finalAmount;
      }
    }
    const refCodeUsed = commissions.length;

    return {
      totalSoldItems: parseFloat(totalSoldItems.toFixed(2)),
      totalRevenue: parseFloat(totalRevenue.toFixed(2)),
      refCodeUsed: parseFloat(refCodeUsed.toFixed(2)),
      estimatedCommissionsAmount: parseFloat(estimatedCommissionsAmount.toFixed(2))
    };
  }

  async getCommissions(affiliateId: string, range: IDateRange, page: number, limit: number) {
    const query = ZnAffiliateCommission.query()
      .where('affiliateId', affiliateId);

    if (range.from) {
      query.where('createdAt', '>=', range.from);
    }

    if (range.to) {
      query.where('createdAt', '<', addDays(range.to, 1));
    }

    if (!range.from && !range.to) {
      query.where('createdAt', '>=', startOfMonth(new Date()));
      query.where('createdAt', '<', addDays(endOfMonth(new Date()), 1));
    }

    query.preload('order').orderBy('createdAt', 'desc');

    return await query.paginate(page, limit);
  }

  async getPayouts(affiliateId: string, range: IDateRange) {
    const query = ZnAffiliateCommissionPayment.query()
      .where('affiliateId', affiliateId);

    if (range.from) {
      query.where('createdAt', '>=', range.from);
    }

    if (range.to) {
      query.where('createdAt', '<', addDays(range.to, 1));
    }

    query.preload('paymentMethod')
    query.orderBy('createdAt', 'desc');

    return await query;
  }

  async registerAffiliate(registrationData: IAffiliateRegistrationData) {
    const registeredAffiliate = await ZnAffiliate.findBy('userId', registrationData.userId);
    if (registeredAffiliate) {
      return {
        success: false,
        message: 'This user is already registered',
        data: registeredAffiliate.serialize()
      };
    }

    const createdAffiliate = await ZnAffiliate.create(registrationData);

    for (const pageLink of registrationData.socialPages) {
      await ZnAffiliateSocialPage.create({ link: pageLink, affiliateId: createdAffiliate.id });
    }

    const updatedAffiliate = await ZnAffiliate.query()
      .where('id', createdAffiliate.id)
      .preload('socialPages')
      .preload('user')
      .firstOrFail();

    if (env.get('DISABLE_AFFILIATE_NOTIFICATIONS') === 'true') {
      // const admins = await ZnAdmin.all()
      const admins = await this.adminNotificationService.getAdminsByPermissions([
        { action: ACTION.READ, resource: RESOURCE.AFFILIATION }
      ])
      admins.forEach(async (admin) => {
        await mail.send(new RegistrationConfirmationNotification(updatedAffiliate.user.firstName, admin.username))
          .then(() => {
            logger.info(`Registration confirmation email has been sent successfully to ${admin.username}`);
          })
          .catch((error) => {
            console.error('Error when sending email:', error);
          });
      });
    } else {
      await mail
        .send(new RegistrationConfirmationNotification(updatedAffiliate.user.firstName, updatedAffiliate.user.email))
        .then(() => {
          logger.info(`Registration confirmation email has been sent successfully to ${updatedAffiliate.user.email}`);
        })
        .catch((error) => {
          console.error('Error when sending email:', error);
        })

      this.sendPushNotification(
        updatedAffiliate.user,
        updatedAffiliate.id,
        'Application Received',
        "Thanks for applying! Our team is reviewing your info. We'll notify you soon."
      );
    }

    // const admins = await ZnAdmin.all()
    const admins = await this.adminNotificationService.getAdminsByPermissions([
      { action: ACTION.READ, resource: RESOURCE.AFFILIATION }
    ])
    admins.forEach(async (admin) => {
      await mail.send(new AdminReceiveRegistrationNotification(
        true,
        admin.name ?? 'Admin',
        admin.username,
        `${updatedAffiliate.user.firstName} ${updatedAffiliate.user.lastName}`,
        updatedAffiliate.user.email,
        updatedAffiliate.socialPages.map((page) => page.link).join(', '),
        updatedAffiliate.createdAt.toFormat('EEE, MMM dd, yyyy, hh:mm a')
      ))
        .then(() => {
          logger.info(`New registration notification email has been sent successfully to ${admin.username}`);
        })
        .catch((error) => {
          console.error('Error when sending email:', error);
        })
    })

    return {
      success: true,
      message: 'Registered successfully',
      data: updatedAffiliate?.serialize()
    };
  }

  async registerAffiliateForExistingUser(userId: string) {
    const user = await ZnUser.findOrFail(userId);

    const existingAffiliate = await ZnAffiliate.findBy('userId', user.id)
    if (existingAffiliate !== null && existingAffiliate !== undefined)
      return existingAffiliate;

    const initialTier = await this.tierService.getStartingTier();

    return await ZnAffiliate.create(
      {
        userId: user.id,
        tierId: initialTier.id,
        registerStatus: EApprovalStatus.APPROVED,
        shareLinkWithRefCode: true
      }
    );
  }

  async updateAffiliateRegistration(registrationData: IAffiliateRegistrationData) {
    const pendingAffiliate = await ZnAffiliate
      .query()
      .preload('socialPages')
      .where('userId', registrationData.userId)
      .firstOrFail();

    pendingAffiliate.registerStatus = EApprovalStatus.PENDING;
    await pendingAffiliate.save();

    pendingAffiliate.socialPages.forEach(async (page) => {
      await page.delete();
    });

    for (const pageLink of registrationData.socialPages) {
      await ZnAffiliateSocialPage.create({ link: pageLink, affiliateId: pendingAffiliate.id });
    }

    const updatedAffiliate = await ZnAffiliate.query()
      .where('id', pendingAffiliate.id)
      .preload('socialPages')
      .preload('user')
      .firstOrFail();

    if (env.get('DISABLE_AFFILIATE_NOTIFICATIONS') === 'true') {
      // const admins = await ZnAdmin.all()
      const admins = await this.adminNotificationService.getAdminsByPermissions([
        { action: ACTION.READ, resource: RESOURCE.AFFILIATION }
      ])
      admins.forEach(async (admin) => {
        await mail
          .send(new RegistrationReapplyNotification(updatedAffiliate.user.firstName, admin.username))
          .then(() => {
            logger.info(`Confirmation email of reapplied registration has been sent successfully to ${admin.username}`);
          })
          .catch((error) => {
            console.error('Error when sending email:', error);
          });
      });
    } else {
      await mail
        .send(new RegistrationReapplyNotification(updatedAffiliate.user.firstName, updatedAffiliate.user.email))
        .then(() => {
          logger.info(`Confirmation email of reapplied registration has been sent successfully to ${updatedAffiliate.user.email}`);
        })
        .catch((error) => {
          console.error('Error when sending email:', error);
        })

      this.sendPushNotification(
        updatedAffiliate.user,
        updatedAffiliate.id,
        'Reapplication Received',
        "Your reapplication is under review. We'll update you once it's processed."
      );
    }

    // const admins = await ZnAdmin.all()
    const admins = await this.adminNotificationService.getAdminsByPermissions([
      { action: ACTION.READ, resource: RESOURCE.AFFILIATION }
    ])
    admins.forEach(async (admin) => {
      await mail.send(new AdminReceiveRegistrationNotification(
        false,
        admin.name ?? 'Admin',
        admin.username,
        `${updatedAffiliate.user.firstName} ${updatedAffiliate.user.lastName}`,
        updatedAffiliate.user.email,
        updatedAffiliate.socialPages.map((page) => page.link).join(', '),
        updatedAffiliate.updatedAt.toFormat('EEE, MMM dd, yyyy, hh:mm a')
      ))
        .then(() => {
          logger.info(`Reapply notification email has been sent successfully to ${admin.username}`);
        })
        .catch((error) => {
          console.error('Error when sending email:', error);
        })
    })

    return updatedAffiliate;
  }

  async updateShareLinkWithRefCode(userId: string, enable: boolean) {
    const affiliate = await ZnAffiliate.findByOrFail('userId', userId);
    affiliate.shareLinkWithRefCode = enable;
    return await affiliate.save();
  }

  generateRandomString(length: number): string {
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    const charactersLength = characters.length;
    let result = '';

    for (let i = 0; i < length; i++) {
      result += characters.charAt(Math.floor(Math.random() * charactersLength));
    }

    return result;
  }

  async verifyAppliedDiscountCode(order: any) {
    const { admin_graphql_api_id, discount_codes } = order;
    const discountCodes = discount_codes.map((discountCode: any) => discountCode.code)

    for (const code of discountCodes) {
      logger.debug('AffiliationService.verifyAppliedDiscountCode() verifying code: %s', code)

      const affiliate = await ZnAffiliate.query()
        .whereHas('refCode', (query) => {
          query.where('code', code);
        })
        .preload('affiliateTier', (query) => {
          query.preload('commissionGroups', (query) => {
            query.preload('products');
          });
        })
        .preload('refCode')
        .first();
      if (!affiliate) return;

      const refCode = affiliate.refCode

      // @ts-ignore
      const order = await ZnOrder.query({ mode: 'write' })
        .where('shopifyId', admin_graphql_api_id)
        .preload('orderDetails', (query) => {
          query.preload('variant');
        })
        .firstOrFail();

      // @ts-ignore
      const existingComission = await ZnAffiliateCommission.query({ mode: 'write' })
        .where('orderId', order.id);
      if (existingComission && existingComission.length > 0) {
        logger.debug(`AffiliationService.verifyAppliedDiscountCode() Commission of order ID ${order.id} is already created`);
        continue;
      }

      const commissionDetailsData = []
      for (const orderDetail of order.orderDetails) {
        const actualPrice = orderDetail.price * orderDetail.currentQuantity - orderDetail.discount

        const belongedCommissionGroup = affiliate.affiliateTier.commissionGroups.find((grp) => grp.products.map(prd => prd.id).includes(orderDetail.variant.productId));
        if (belongedCommissionGroup) {
          const commissionAmount = actualPrice > 0 ? actualPrice * belongedCommissionGroup.commissionRate : 0;

          const commissionDetail = {
            commissionAmount: commissionAmount,
            commissionRate: belongedCommissionGroup.commissionRate,
            orderDetailId: orderDetail.id
          }
          commissionDetailsData.push(commissionDetail)
        }
      }
      const totalCommissionAmount = commissionDetailsData.reduce((total, com) => total + com.commissionAmount, 0)
      logger.debug('AffiliationService.verifyAppliedDiscountCode() commissionDetailsData: %s', JSON.stringify(commissionDetailsData, null, 2))
      logger.debug('AffiliationService.verifyAppliedDiscountCode() totalCommissionAmount: %d', totalCommissionAmount)

      const commission = await this.commissionService.createCommission(affiliate.id, refCode.id, order.id, totalCommissionAmount)
      for (const commissionDetailData of commissionDetailsData) {
        await this.commissionService.createCommissionDetail(commission.id, commissionDetailData.orderDetailId, commissionDetailData.commissionAmount, commissionDetailData.commissionRate);
      }

      await affiliate.save();
    }
  }

  async verifyRegistrationStatus(affiliateId: string, data: any) {
    const payload = await approvalRegistrationValidator.validate(data);

    const registeringAffiliate = await ZnAffiliate.query()
      .where('id', affiliateId)
      .preload('user')
      .firstOrFail();

    if (registeringAffiliate.registerStatus == EApprovalStatus.APPROVED)
      throw new Error('Affiliate was already approved');

    if (payload.approval == EApprovalStatus.APPROVED) {
      const initialTier = await this.tierService.getStartingTier();
      registeringAffiliate.tierId = initialTier.id;

      if (payload.refCode) {
        await registeringAffiliate.load('affiliateTier', (query) => {
          query.preload('discountCollection');
        });
        await this.refCodeService.assignRefCode(registeringAffiliate, payload.refCode);
      }
      if (env.get('DISABLE_AFFILIATE_NOTIFICATIONS') === 'true') {
        // const admins = await ZnAdmin.all()
        const admins = await this.adminNotificationService.getAdminsByPermissions([
          { action: ACTION.READ, resource: RESOURCE.AFFILIATION }
        ])
        admins.forEach(async (admin) => {
          await mail.send(new RegistrationApprovedNotification(registeringAffiliate.user.firstName, payload.refCode ?? '', admin.username))
            .then(() => {
              logger.info(`Registration approved email has been sent successfully to ${admin.username}`);
            })
            .catch((error) => {
              console.error('Error when sending email', error);
            });
        });
      } else {
        await mail.send(new RegistrationApprovedNotification(registeringAffiliate.user.firstName, payload.refCode ?? '', registeringAffiliate.user.email))
          .then(() => {
            logger.info(`Registration approved email has been sent successfully to ${registeringAffiliate.user.email}`);
          })
          .catch((error) => {
            console.error('Error when sending email', error);
          })

        this.sendPushNotification(
          registeringAffiliate.user,
          registeringAffiliate.id,
          'Welcome Aboard!',
          payload.refCode !== undefined ?
            'Your application is approved! Get your unique referral code and start earning.' :
            "Your registration is approved! Your referral code is being set up. We'll notify you once it's ready."
        );
      }

    } else {
      if (env.get('DISABLE_AFFILIATE_NOTIFICATIONS') === 'true') {
        // const admins = await ZnAdmin.all()
        const admins = await this.adminNotificationService.getAdminsByPermissions([
          { action: ACTION.READ, resource: RESOURCE.AFFILIATION }
        ])
        admins.forEach(async (admin) => {
          await mail.send(new RegistrationRejectNotification(registeringAffiliate.user.firstName, payload.note, admin.username))
            .then(() => {
              logger.info(`Registration reject email has been sent successfully`);
            })
            .catch((error) => {
              console.error('Error when sending email', error);
            })
        });
      } else {
        await mail.send(new RegistrationRejectNotification(registeringAffiliate.user.firstName, payload.note, registeringAffiliate.user.email))
          .then(() => {
            logger.info(`Registration reject email has been sent successfully`);
          })
          .catch((error) => {
            console.error('Error when sending email', error);
          })

        this.sendPushNotification(
          registeringAffiliate.user,
          registeringAffiliate.id,
          'Application Update',
          'Sorry, your application was not approved. Contact support for details.'
        );
      }
    }

    registeringAffiliate.registerStatus = payload.approval;
    registeringAffiliate.registerNote = payload.note;

    return await registeringAffiliate.save();
  }

  async setRefCode(affiliateId: string, refCodeString: string) {
    const affiliate = await ZnAffiliate.query()
      .where('id', affiliateId)
      .preload('user')
      .preload('affiliateTier', (query) => {
        query.preload('discountCollection')
      })
      .preload('refCode')
      .firstOrFail();

    const isFirstRefCode = affiliate.refCode === null || affiliate.refCode === undefined;

    const updatedAffiliate = await this.refCodeService.assignRefCode(affiliate, refCodeString);

    if (env.get('DISABLE_AFFILIATE_NOTIFICATIONS') === 'true') {
      // const admins = await ZnAdmin.all()
      const admins = await this.adminNotificationService.getAdminsByPermissions([
        { action: ACTION.READ, resource: RESOURCE.AFFILIATION }
      ])
      admins.forEach(async (admin) => {
        await mail.send(new RefCodeAssignedNotification(updatedAffiliate.user.firstName, updatedAffiliate.refCode.code, isFirstRefCode, admin.username))
          .then(() => {
            logger.info(`REF code email has been sent successfully to ${admin.username}`);
          })
          .catch((error) => {
            console.error('Error when sending email', error);
          })
      });
    } else {
      await mail.send(new RefCodeAssignedNotification(updatedAffiliate.user.firstName, updatedAffiliate.refCode.code, isFirstRefCode, updatedAffiliate.user.email))
        .then(() => {
          logger.info(`REF code email has been sent successfully to ${updatedAffiliate.user.email}`);
        })
        .catch((error) => {
          console.error('Error when sending email', error);
        })

      this.sendPushNotification(
        updatedAffiliate.user,
        updatedAffiliate.id,
        isFirstRefCode ? 'Your Referral Code Is Ready' : 'Your New Referral Code',
        isFirstRefCode ?
          "Your new referral code has been assigned. Share it to start earning commissions." :
          "Your updated referral code is now active. Use it to earn commissions."
      );
    }

    return updatedAffiliate;
  }

  async updateTier(affiliateId: string, data: any) {
    const affiliate = await ZnAffiliate.query()
      .where('id', affiliateId)
      .preload('affiliateTier')
      .preload('refCode')
      .preload('user')
      .firstOrFail();

    if (!affiliate.affiliateTier) {
      throw new Error(`No tier is associated with affiliate ${affiliateId}`);
    }

    const tierAction = data.tierAction;
    if (!tierAction) {
      throw new Error(`No tier action is specified`);
    }

    const tierQuery = ZnAffiliateTier.query();

    if (tierAction == ETierAction.UPGRADE) {
      tierQuery
        .where('tier', '>', affiliate.affiliateTier.tier)
        .orderBy('tier', 'asc');
    } else if (tierAction == ETierAction.DOWNGRADE) {
      tierQuery
        .where('tier', '<', affiliate.affiliateTier.tier)
        .orderBy('tier', 'desc');
    } else {
      throw new Error(`Tier action is unknown`);
    }

    const newTier = await tierQuery.firstOrFail();

    if (affiliate.refCode && newTier.defaultDiscount !== affiliate.affiliateTier.defaultDiscount) {
      const generatedCode = await this.refCodeService.cloneRefCode(affiliate, newTier);
      if (!generatedCode) {
        throw new Error('Failed to create REF code for new tier.');
      }
      affiliate.refCodeId = generatedCode.id;
    }

    const isUpgrading = newTier.id > affiliate.tierId;
    const commissionPercent = `${newTier.defaultCommission * 100}%`;
    const discountPercent = `${newTier.defaultDiscount * 100}%`;

    if (env.get('DISABLE_AFFILIATE_NOTIFICATIONS') === 'true') {
      // const admins = await ZnAdmin.all()
      const admins = await this.adminNotificationService.getAdminsByPermissions([
        { action: ACTION.READ, resource: RESOURCE.AFFILIATION }
      ])
      admins.forEach(async (admin) => {
        await mail.send(new TierChangedNotification(affiliate.user.firstName, isUpgrading, commissionPercent, discountPercent, admin.username))
          .then(() => {
            logger.info(`Tier changing email has been sent successfully to ${admin.username}`);
          })
          .catch((error) => {
            console.error('Error when sending email:', error);
          })
      });
    } else {
      await mail.send(new TierChangedNotification(affiliate.user.firstName, isUpgrading, commissionPercent, discountPercent, affiliate.user.email))
        .then(() => {
          logger.info(`Tier changing email has been sent successfully to ${affiliate.user.email}`);
        })
        .catch((error) => {
          console.error('Error when sending email:', error);
        })

      this.sendPushNotification(
        affiliate.user,
        affiliate.id,
        isUpgrading ? 'Tier Upgraded!' : 'Tier Changed',
        isUpgrading ?
          'Congratulations! Your affiliate tier has been upgraded. Check your new benefits and rates.' :
          'Your affiliate tier has been downgraded. Review your updated benefits and rates in your dashboard.'
      );
    }

    affiliate.tierId = newTier.id;

    return affiliate.save();
  }

  async payForCommissions(affiliateId: string, amount: number, paymentMethodId: string) {
    const affiliate = await ZnAffiliate.query()
      .where('id', affiliateId)
      .preload('user')
      .firstOrFail();

    if (!affiliate.balance || affiliate.balance == 0) {
      return {
        'success': false,
        'reason': 'Empty balance'
      }
    }

    if (amount > affiliate.balance) {
      return {
        'success': false,
        'reason': 'Not enough balance'
      }
    }

    const paymentMethod = await ZnPaymentMethod.findOrFail(paymentMethodId)

    const payment = await ZnAffiliateCommissionPayment.create(
      {
        affiliateId: affiliate.id,
        paymentMethodId: paymentMethod.id,
        amount: amount
      }
    );

    if (affiliate.totalPayments !== null && affiliate.totalPayments !== undefined && !Number.isNaN(affiliate.totalPayments))
      affiliate.totalPayments += payment.amount
    else
      affiliate.totalPayments = payment.amount
    await affiliate.save();
    if (env.get('DISABLE_AFFILIATE_NOTIFICATIONS') === 'true') {
      // const admins = await ZnAdmin.all()
      const admins = await this.adminNotificationService.getAdminsByPermissions([
        { action: ACTION.READ, resource: RESOURCE.AFFILIATION }
      ])
      admins.forEach(async (admin) => {
        await mail.send(new PayoutNotification(affiliate.user.firstName, amount, paymentMethod.paymentTypeName, affiliate.balance, admin.username))
          .then(() => {
            logger.info(`Payout email has been sent successfully to ${admin.username}`);
          })
          .catch((error) => {
            console.error('Error when sending email:', error);
          });
      });
    } else {
      await mail.send(new PayoutNotification(affiliate.user.firstName, amount, paymentMethod.paymentTypeName, affiliate.balance, affiliate.user.email))
        .then(() => {
          logger.info(`Payout email has been sent successfully to ${affiliate.user.email}`);
        })
        .catch((error) => {
          console.error('Error when sending email:', error);
        })

      this.sendPushNotification(
        affiliate.user,
        'payouts',
        'Payout Successful',
        `Your payout of $${amount} has been processed. Check your account for details.`
      );
    }

    return { 'success': true }
  }

  async previewSyncData(affiliateId: string) {
    const affiliate = await ZnAffiliate.query()
      .where('id', affiliateId)
      .preload('commissions', (query) => {
        query
          .preload('commissionDetails', (query) => {
            query.preload('orderDetail');
          });
      })
      .preload('commissionPayments')
      .firstOrFail()

    const updatedData = {
      itemsSold: 0,
      gmv: 0,
      refCodesUsed: 0,
      totalCommissionsAmount: 0,
      totalPayoutAmount: 0,
    }

    for (const commision of affiliate.commissions) {
      if (commision.status == EApprovalStatus.APPROVED) {
        for (const commissionDetail of commision.commissionDetails) {
          if (commissionDetail.orderDetail.currentQuantity > 0 && commissionDetail.commissionRate > 0) {
            updatedData.itemsSold += commissionDetail.orderDetail.currentQuantity;
            updatedData.gmv += commissionDetail.orderDetail.price * commissionDetail.orderDetail.currentQuantity - commissionDetail.orderDetail.discount;
          }
        }
        updatedData.refCodesUsed += 1
        updatedData.totalCommissionsAmount += commision.finalAmount
      }
    }

    updatedData.totalPayoutAmount = affiliate.commissionPayments
      .map(payout => payout.amount)
      .reduce((total, amount) => total + amount, 0)

    return {
      itemsSold: {
        current: affiliate.itemSold,
        new: updatedData.itemsSold,
      },
      gmv: {
        current: affiliate.grossMerchandiseValue,
        new: updatedData.gmv,
      },
      refCodesUsed: {
        current: affiliate.refCodeUsed,
        new: updatedData.refCodesUsed
      },
      commissionAmount: {
        current: affiliate.totalCommissions,
        new: updatedData.totalCommissionsAmount
      },
      payoutAmount: {
        current: affiliate.totalPayments,
        new: updatedData.totalPayoutAmount
      }
    }
  }

  async proceedSyncData(affiliateId: string, {
    itemsSold,
    gmv,
    refCodesUsed,
    totalCommissionsAmount,
    totalPayoutAmount,
  }: any) {
    const affiliate = await ZnAffiliate.query()
      .where('id', affiliateId)
      .preload('commissions')
      .preload('commissionPayments')
      .firstOrFail()

    affiliate.itemSold = itemsSold
    affiliate.grossMerchandiseValue = gmv
    affiliate.refCodeUsed = refCodesUsed
    affiliate.totalCommissions = totalCommissionsAmount
    affiliate.totalPayments = totalPayoutAmount
    await affiliate.save()

    return affiliate
  }

  async delete(affiliateId: string) {
    const affiliate = await ZnAffiliate.find(affiliateId);
    if (!affiliate) return;
    await this.refCodeService.delete(affiliate.refCodeId);
    await affiliate.softDelete();
  }

  private async sendPushNotification(user: ZnUser, resourceId: string, title: string, description: string) {
    const notification = await Notification.create({
      type: NOTIFICATION_TYPE.AFFILIATE,
      userId: user.id,
      resourceId: resourceId,
      title: title,
      description: description,
    })

    if (notification) {
      const notificationService = new NotificationService()
      await notificationService.send([user], [notification])
    }
  }

  async getCommissionAndDiscountPercentage({ userId, productId }:
    { userId: string, productId: string }) {
    const affiliate = await ZnAffiliate.query()
      .where('userId', userId)
      .preload('affiliateTier', (query) => {
        query.preload('commissionGroups', (query) => {
          query.preload('products')
        })
        query.preload('discountCollection', (query) => {
          query.preload('products')
        })
      })
      .first()

    if (!affiliate) {
      return {
        commissionPercent: 0,
        discountPercent: 0,
      }
    }

    let commissionPercent = 0;
    for (const group of affiliate.affiliateTier.commissionGroups) {
      const productIsInCommissionGroup = group.products.map(prd => prd.id).some(id => id === productId);
      if (productIsInCommissionGroup) {
        commissionPercent = group.commissionRate;
        break;
      }
    }

    const productIsInDiscountCollection = affiliate.affiliateTier.discountCollection?.products.some(
      (product) => product.id === productId
    );
    const discountPercent = productIsInDiscountCollection ? affiliate.affiliateTier.defaultDiscount : 0;

    return {
      commissionPercent: commissionPercent,
      discountPercent: discountPercent,
    }
  }
}