import ZnAffiliate from "#models/zn_affiliate"
import ZnAffiliateRefCode from "#models/zn_affiliate_refcode"
import ZnAffiliateTier from "#models/zn_affiliate_tier"
import { ShopifyService } from "#services/shopify/shopify_service"
import { DateTime } from "luxon"

export interface IAffiliateRegistrationData {
  userId: string,
  socialPages: string[]
}

export interface IDiscountCodeBasicInput {
  title: string,
  code: string,
  startsAt: DateTime,
  endsAt?: DateTime,
  customerGets: IDiscountCustomerGetsInput,
  customerSelection: IDiscountCustomerSelectionInput
}

export interface IDiscountCustomerGetsInput {
  value: IDiscountCodeCustomerGetValue,
  items: IDiscountCodeItemsInput
}

export interface IDiscountCodeCustomerGetValue {
  percentage: number
}

export interface IDiscountCodeItemsInput {
  all: boolean
}

export interface IDiscountCustomerSelectionInput {
  all: boolean
}

export class AffiliateRefCodeService {
  private shopifyService: ShopifyService;

  constructor() {
    this.shopifyService = new ShopifyService();
  }

  async getRefCode(refCodeId: string) {
    return await ZnAffiliateRefCode.findOrFail(refCodeId);
  }

  async createRefCode({ affiliateId, tierId, refCode }: any, oldRefCode: ZnAffiliateRefCode | null) {
    const affiliate = await ZnAffiliate.query()
      .where('id', affiliateId)
      .preload('user')
      .firstOrFail();

    const tier = await ZnAffiliateTier.query()
      .where('id', tierId ? tierId : affiliate.tierId)
      .preload('commissionGroups')
      .preload('discountCollection')
      .firstOrFail();

    let shopifyDiscountCodeId = ''
    if (tier.discountCollection.shopifyCollectionId) {
      shopifyDiscountCodeId = await this.createShopifyDiscountCode(
        `${affiliate.user.firstName} ${affiliate.user.lastName}`,
        tier.defaultDiscount,
        tier.discountCollection.shopifyCollectionId,
        refCode
      );
      if (!shopifyDiscountCodeId) {
        return null;
      }

      if (oldRefCode !== null && oldRefCode !== undefined) {
        await this.shopifyService.deactiveDiscountCode(oldRefCode.shopifyId);
      }
    }

    return await ZnAffiliateRefCode.create({
      code: refCode,
      shopifyId: shopifyDiscountCodeId,
    });
  }

  async assignRefCode(affiliate: ZnAffiliate, refCodeString: string) {
    const shopifyDiscountCode = await this.shopifyService.getDiscountCode(refCodeString);

    if (shopifyDiscountCode === null || shopifyDiscountCode === undefined) {
      if (affiliate.affiliateTier.discountCollection === null || affiliate.affiliateTier.discountCollection === undefined) {
        throw new Error(`Tier ${affiliate.affiliateTier.id} doesn't contain discount collection`);
      }

      if (affiliate.affiliateTier.discountCollection.shopifyCollectionId === null || affiliate.affiliateTier.discountCollection.shopifyCollectionId === undefined) {
        throw new Error(`Discount collection of tier ${affiliate.affiliateTier.id} doesn't have a corresponding Shopify collection`);
      }

      const shopifyDiscountCodeId = await this.createShopifyDiscountCode(
        `${affiliate.user.firstName} ${affiliate.user.lastName}`,
        affiliate.affiliateTier.defaultDiscount,
        affiliate.affiliateTier.discountCollection.shopifyCollectionId,
        refCodeString
      );

      const refCode = await ZnAffiliateRefCode.create({
        code: refCodeString,
        shopifyId: shopifyDiscountCodeId,
      });

      const oldRefCode = affiliate.refCode;

      affiliate.refCodeId = refCode.id;
      await affiliate.save();
      await affiliate.load('refCode');

      if (oldRefCode !== null && oldRefCode !== undefined) {
        await this.shopifyService.deactiveDiscountCode(oldRefCode.shopifyId);
      }

    } else {
      if (shopifyDiscountCode.codeDiscount.status !== 'ACTIVE') {
        throw new Error(`This Shopify discount code is not active.`);
      }

      let refCode = await ZnAffiliateRefCode.findBy('shopifyId', shopifyDiscountCode.id);

      if (refCode !== null && refCode !== undefined) {
        throw new Error(`Discount code ${refCodeString} is already corresponding to REF Code ID ${refCode.id}`);
      }

      refCode = await ZnAffiliateRefCode.create({
        code: refCodeString,
        shopifyId: shopifyDiscountCode.id,
      });

      const oldRefCode = affiliate.refCode;

      affiliate.refCodeId = refCode.id;
      await affiliate.save();
      await affiliate.load('refCode');

      if (oldRefCode !== null && oldRefCode !== undefined) {
        await this.shopifyService.deactiveDiscountCode(oldRefCode.shopifyId);
      }

    }

    return affiliate;
  }

  async cloneRefCode(affiliate: ZnAffiliate, tier: ZnAffiliateTier) {
    if (!affiliate) {
      throw new Error('Affiliate is null.');
    }
    if (!affiliate.refCode) {
      throw new Error('REF code is null.');
    }
    if (!tier) {
      throw new Error('Tier is null.');
    }
    if (!tier.discountCollection) {
      throw new Error('Discount Collection is null.');
    }
    if (!tier.discountCollection.shopifyCollectionId) {
      throw new Error('Discount Collection ID is null.');
    }

    await affiliate.load('user');
    const shopifyDiscountCodeId = await this.createShopifyDiscountCode(
      `${affiliate.user.firstName} ${affiliate.user.lastName}`,
      tier.defaultDiscount,
      tier.discountCollection.shopifyCollectionId,
      affiliate.refCode.code
    );
    if (!shopifyDiscountCodeId) {
      return null;
    }

    await this.shopifyService.deactiveDiscountCode(affiliate.refCode.shopifyId);

    return await ZnAffiliateRefCode.create({
      code: affiliate.refCode.code,
      shopifyId: shopifyDiscountCodeId,
    });
  }

  async createShopifyDiscountCode(userFullName: string, discountRate: number, shopifyCollectionId: string, refCode: string) {
    try {
      const params = {
        title: `Affiliate ${userFullName} ${discountRate * 100}%`,
        code: refCode,
        startsAt: DateTime.now(),
        customerGets: {
          value: {
            percentage: discountRate
          },
          items: {
            collections: {
              add: [shopifyCollectionId]
            }
          },
        },
        customerSelection: {
          all: true
        },
        usageLimit: null,
        appliesOncePerCustomer: false,
        combinesWith: {
          orderDiscounts: true,
          productDiscounts: true,
          shippingDiscounts: true
        }
      }

      return await this.shopifyService.createDiscountCode(params)

    } catch (error) {
      console.error(error);
      throw new Error('Cannot create Shopify discount code');
    }
  }

  async delete(refCodeId: string) {
    const refCode = await ZnAffiliateRefCode.find(refCodeId);
    if (!refCode) return;
    await refCode.softDelete();
  }
}