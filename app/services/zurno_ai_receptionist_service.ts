import twilio from 'twilio'
import OpenAI from 'openai'
import fs from 'node:fs'
import axios from 'axios'
import { inject } from '@adonisjs/core'

import ZnAIAssistant, { EAIAssistantRole } from '#models/zn_ai_assistant'
import ZurnoAssistantService from '#services/zurno_assistant_service'
import ZnCall from '#models/zn_call'
import ZnCallMessage from '#models/zn_call_message'
import ZnStore from '#models/zn_store'
import SessionManagementService from './ai/session_management_service.js'

export interface ConversationRequest {
  message: string
  storeId: string
  customerPhone?: string
  customerId?: string
  sessionId?: string
  callId?: string
}

export interface ConversationResponse {
  response: string
  nextAction: 'continue' | 'end_conversation' | 'transfer_to_human' | 'collect_info'
  suggestions?: string[]
  quickActions?: Array<{
    label: string
    action: string
    data?: any
  }>
  sessionId: string
}

@inject()
export class ZurnoAIReceptionistService {
  private voiceResponse
  private openai
  private baseRoute = '/v1/receptions/'
  private zurnoAssistanceService

  constructor(private sessionService: SessionManagementService) {
    this.voiceResponse = new twilio.twiml.VoiceResponse()
    this.openai = new OpenAI()
    this.zurnoAssistanceService = new ZurnoAssistantService()
  }

  /**
   * Enhanced conversation processing with AI capabilities
   */
  async processConversation(request: ConversationRequest): Promise<ConversationResponse> {
    try {
      // Get or create session
      let session
      if (request.sessionId) {
        session = await this.sessionService.getSession(request.sessionId)
      }

      if (!session) {
        // Find store by phone number or use provided storeId
        const store = await this.findStoreByPhone(request.storeId)
        if (!store) {
          throw new Error('Store not found')
        }

        session = await this.sessionService.createSession(
          store.id,
          request.customerPhone,
          undefined // customerId will be resolved later
        )
      }

      // Process message with basic AI processing
      const result = await this.processMessageBasic(request.message, session.storeId)

      return {
        response: result.response,
        nextAction: result.nextAction,
        suggestions: result.suggestions || [],
        sessionId: session.sessionId,
      }
    } catch (error) {
      console.error('Conversation processing error:', error)

      // Fallback to basic response
      return {
        response:
          "I'm sorry, I'm having trouble understanding. Could you please repeat that or try asking in a different way?",
        nextAction: 'continue',
        suggestions: ['Try again', 'Speak to agent'],
        sessionId: request.sessionId || 'error',
      }
    }
  }

  /**
   * Find store by phone number (for incoming calls)
   */
  private async findStoreByPhone(phoneOrStoreId: string): Promise<ZnStore | null> {
    // If it looks like a UUID, treat as storeId
    if (phoneOrStoreId.length === 36 && phoneOrStoreId.includes('-')) {
      return await ZnStore.find(phoneOrStoreId)
    }

    // Otherwise, search by phone number
    return await ZnStore.query()
      .where('phone', phoneOrStoreId)
      .orWhere('phone', phoneOrStoreId.replace(/\D/g, '')) // Remove non-digits
      .first()
  }

  /**
   * Basic message processing with OpenAI
   */
  private async processMessageBasic(
    message: string,
    storeId: string
  ): Promise<{
    response: string
    nextAction: ConversationResponse['nextAction']
    suggestions?: string[]
  }> {
    try {
      // Get store information
      const store = await ZnStore.find(storeId)
      const storeName = store?.name || 'our business'

      // Use OpenAI to generate a response
      const completion = await this.openai.chat.completions.create({
        model: 'gpt-4',
        messages: [
          {
            role: 'system',
            content: `You are a helpful AI receptionist for ${storeName}. You help customers with appointment booking, store information, and general inquiries. Keep responses concise and friendly. If asked about booking appointments, ask for their preferred date and time.`,
          },
          {
            role: 'user',
            content: message,
          },
        ],
        max_tokens: 150,
        temperature: 0.7,
      })

      const response =
        completion.choices[0]?.message?.content ||
        "I apologize, but I'm having trouble understanding. Could you please repeat that?"

      // Simple intent detection based on keywords
      const lowerMessage = message.toLowerCase()
      let nextAction: ConversationResponse['nextAction'] = 'continue'
      let suggestions: string[] = []

      if (
        lowerMessage.includes('book') ||
        lowerMessage.includes('appointment') ||
        lowerMessage.includes('schedule')
      ) {
        nextAction = 'collect_info'
        suggestions = ['Book appointment', 'Check availability', 'View services']
      } else if (
        lowerMessage.includes('bye') ||
        lowerMessage.includes('goodbye') ||
        lowerMessage.includes('thanks')
      ) {
        nextAction = 'end_conversation'
      } else if (
        lowerMessage.includes('help') ||
        lowerMessage.includes('human') ||
        lowerMessage.includes('agent')
      ) {
        nextAction = 'transfer_to_human'
        suggestions = ['Speak to agent', 'Get help', 'Call store']
      }

      return {
        response,
        nextAction,
        suggestions,
      }
    } catch (error) {
      console.error('Basic message processing error:', error)
      return {
        response:
          "I apologize, but I'm experiencing some technical difficulties. Please try again or speak with one of our team members.",
        nextAction: 'transfer_to_human',
        suggestions: ['Try again', 'Speak to agent'],
      }
    }
  }

  sayAndListen(message: any) {
    if (message) {
      this.voiceResponse.say({ voice: 'Polly.Joanna' }, message)
    }

    this.voiceResponse.record({
      timeout: 2,
      maxLength: 5,
      action: this.baseRoute + 'process-flow',
      transcribe: false,
      playBeep: true,
    })

    return this.voiceResponse.toString()
  }

  async translateVoiceToText(recordingUrl: any) {
    const filePath = './data/user_audio.wav'
    await this.waitForTwilioAudio(recordingUrl, filePath)

    // 2. Transcribe using OpenAI Whisper
    const transcription = await this.openai.audio.transcriptions.create({
      file: fs.createReadStream(filePath),
      model: 'whisper-1',
    })

    return transcription.text
  }

  redialToAgent() {
    this.voiceResponse.say({ voice: 'Polly.Joanna' }, 'Connecting you to a Zurno representative.')
    this.voiceResponse.dial(process.env.SUPPORT_PHONE_NUMBER) // agent phone or Twilio SIP endpoint
    return this.voiceResponse.toString()
  }

  handleMakeAppointment() {
    this.voiceResponse.say({ voice: 'Polly.Joanna' }, 'Making appointment.')
    return this.voiceResponse.toString()
  }

  async handleReception(room: ZnCall, message: ZnCallMessage, userSaid: string) {
    try {
      // Use enhanced AI conversation processing
      const conversationRequest: ConversationRequest = {
        message: userSaid,
        storeId: room.toNumber, // Use phone number to find store
        customerPhone: room.fromNumber,
        sessionId: room.threadId,
        callId: room.id,
      }

      const aiResponse = await this.processConversation(conversationRequest)

      // Store the AI response
      message.reply = aiResponse.response
      await message.save()

      console.log('Enhanced AI answer:', aiResponse.response)
      console.log('Next action:', aiResponse.nextAction)

      return aiResponse
    } catch (error) {
      console.error('Enhanced reception handling error:', error)

      // Fallback to basic AI assistant
      const assistant = await ZnAIAssistant.findBy({ role: EAIAssistantRole.CUSTOMER_SERVICE })
      let answer = 'I dont have answer'
      if (assistant) {
        const parsed = await this.zurnoAssistanceService.getAIAnswer(
          assistant,
          room.threadId,
          userSaid
        )
        answer = parsed?.text || 'No answer'

        // Store new message
        message.reply = answer
        await message.save()
      }
      console.log('Fallback AI answer:', answer)

      return {
        response: answer,
        nextAction: 'continue',
        sessionId: room.threadId,
      }
    }
  }

  handleLoop() {
    this.voiceResponse.redirect(this.baseRoute + 'process-flow')
    return this.voiceResponse.toString()
  }

  handleBye() {
    this.voiceResponse.say({ voice: 'Polly.Joanna' }, 'Bye. Have a great day!')
    return this.voiceResponse.toString()
  }

  handlePause(userMessage: ZnCallMessage) {
    this.voiceResponse.say({ voice: 'Polly.Joanna' }, 'Please hold while we process your request.')
    this.voiceResponse.pause({ length: 5 })
    this.voiceResponse.redirect(this.baseRoute + 'process-reception/' + userMessage.id)
    return this.voiceResponse.toString()
  }
  async waitForTwilioAudio(url: string, savePath: fs.PathLike, maxAttempts = 5, delayMs = 1500) {
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        const response = await axios.get(url, { responseType: 'stream' })

        await new Promise((resolve, reject) => {
          const writer = fs.createWriteStream(savePath)
          response.data.pipe(writer)
          // @ts-ignore
          writer.on('finish', resolve)
          writer.on('error', reject)
        })

        return true // success
      } catch (err) {
        if (attempt === maxAttempts)
          throw new Error(`Failed to download Twilio file after ${maxAttempts} attempts`)
        if (err.response?.status === 404) {
          console.log(`Recording not ready (attempt ${attempt}), retrying in ${delayMs}ms...`)
          await new Promise((res) => setTimeout(res, delayMs))
        } else {
          throw err
        }
      }
    }
  }
}
