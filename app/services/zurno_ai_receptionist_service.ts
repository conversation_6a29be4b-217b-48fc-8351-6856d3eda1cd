import twilio from 'twilio'
import OpenAI from 'openai'
import fs from 'node:fs'
import axios from 'axios'
import { inject } from '@adonisjs/core'
import { DateTime } from 'luxon'

import ZnAIAssistant, { EAIAssistantRole } from '#models/zn_ai_assistant'
import ZurnoAssistantService from '#services/zurno_assistant_service'
import ZnCall from '#models/zn_call'
import ZnCallMessage from '#models/zn_call_message'
import ZnStore from '#models/zn_store'
import ZnStoreService from '#models/store_service/zn_store_service'
import ZnStoreServiceCategory from '#models/store_service/zn_store_service_category'
import ZnStorePackage from '#models/store_service/zn_store_package'
import SessionManagementService from './ai/session_management_service.js'
import AppointmentService from '#services/store_service/appointment_service'
import { CustomerService } from '../../services/customer/customer_service.js'

export interface ConversationRequest {
  message: string
  storeId: string
  customerPhone?: string
  customerId?: string
  sessionId?: string
  callId?: string
}

export interface ConversationResponse {
  response: string
  nextAction: 'continue' | 'end_conversation' | 'transfer_to_human' | 'collect_info'
  suggestions?: string[]
  quickActions?: Array<{
    label: string
    action: string
    data?: any
  }>
  sessionId: string
}

@inject()
export class ZurnoAIReceptionistService {
  private voiceResponse
  private openai
  private baseRoute = '/v1/receptions/'
  private zurnoAssistanceService
  private appointmentService: AppointmentService
  private customerService: CustomerService

  constructor(private sessionService: SessionManagementService) {
    this.voiceResponse = new twilio.twiml.VoiceResponse()
    this.openai = new OpenAI()
    this.zurnoAssistanceService = new ZurnoAssistantService()
    this.appointmentService = new AppointmentService()
    this.customerService = new CustomerService()
  }

  /**
   * Enhanced conversation processing with full AI booking capabilities
   */
  async processConversation(request: ConversationRequest): Promise<ConversationResponse> {
    try {
      // Get or create session
      let session
      if (request.sessionId) {
        session = await this.sessionService.getSession(request.sessionId)
      }

      if (!session) {
        // Find store by phone number or use provided storeId
        const store = await this.findStoreByPhone(request.storeId)
        if (!store) {
          throw new Error('Store not found')
        }

        session = await this.sessionService.createSession(
          store.id,
          request.customerPhone,
          undefined // customerId will be resolved later
        )
      }

      // Determine processing method based on conversation state
      const currentState = session.context.conversationState
      let result

      // Use enhanced booking flow for booking-related states
      if (
        currentState &&
        [
          'service_selection',
          'datetime_selection',
          'customer_info_collection',
          'booking_confirmation',
        ].includes(currentState)
      ) {
        result = await this.processBookingFlow(
          session.sessionId,
          request.message,
          request.customerPhone || ''
        )
      } else {
        // Use basic processing for general inquiries and initial intent detection
        result = await this.processMessageBasic(request.message, session.storeId)

        // If user wants to book, transition to booking flow
        if (
          result.nextAction === 'collect_info' &&
          (request.message.toLowerCase().includes('book') ||
            request.message.toLowerCase().includes('appointment'))
        ) {
          await this.sessionService.updateConversationState(session.sessionId, 'service_selection')
        }
      }

      return {
        response: result.response,
        nextAction: result.nextAction,
        suggestions: result.suggestions || [],
        sessionId: session.sessionId,
      }
    } catch (error) {
      console.error('Conversation processing error:', error)

      // Fallback to basic response
      return {
        response:
          "I'm sorry, I'm having trouble understanding. Could you please repeat that or try asking in a different way?",
        nextAction: 'continue',
        suggestions: ['Try again', 'Speak to agent'],
        sessionId: request.sessionId || 'error',
      }
    }
  }

  /**
   * Find store by phone number (for incoming calls)
   */
  private async findStoreByPhone(phoneOrStoreId: string): Promise<ZnStore | null> {
    // If it looks like a UUID, treat as storeId
    if (phoneOrStoreId.length === 36 && phoneOrStoreId.includes('-')) {
      return await ZnStore.find(phoneOrStoreId)
    }

    // Otherwise, search by phone number
    return await ZnStore.query()
      .where('phone', phoneOrStoreId)
      .orWhere('phone', phoneOrStoreId.replace(/\D/g, '')) // Remove non-digits
      .first()
  }

  /**
   * Enhanced message processing with full business logic integration
   */
  private async processMessageBasic(
    message: string,
    storeId: string
  ): Promise<{
    response: string
    nextAction: ConversationResponse['nextAction']
    suggestions?: string[]
  }> {
    try {
      // Get store information and services
      const store = await ZnStore.find(storeId)
      const storeName = store?.name || 'our business'
      const storeServices = await this.getStoreServices(storeId)

      // Create context for AI with store services
      const servicesContext =
        storeServices.services.length > 0
          ? `Available services: ${storeServices.services.map((s) => `${s.name} ($${s.price}, ${s.duration} min)`).join(', ')}`
          : 'Services information not available'

      // Enhanced AI processing with business context
      const completion = await this.openai.chat.completions.create({
        model: 'gpt-4',
        messages: [
          {
            role: 'system',
            content: `You are a helpful AI receptionist for ${storeName}. You help customers with appointment booking, store information, and general inquiries.

${servicesContext}

When customers ask about services, provide specific pricing and duration. When they want to book, ask for:
1. Which service they want
2. Preferred date and time
3. Their contact information if needed

Keep responses concise and friendly. Always be helpful and professional.`,
          },
          {
            role: 'user',
            content: message,
          },
        ],
        max_tokens: 200,
        temperature: 0.7,
      })

      let response =
        completion.choices[0]?.message?.content ||
        "I apologize, but I'm having trouble understanding. Could you please repeat that?"

      // Enhanced intent detection and response
      const lowerMessage = message.toLowerCase()
      let nextAction: ConversationResponse['nextAction'] = 'continue'
      let suggestions: string[] = []

      // Service inquiry handling
      if (lowerMessage.includes('service') || lowerMessage.includes('what do you offer')) {
        if (storeServices.services.length > 0) {
          const serviceList = storeServices.services
            .slice(0, 3)
            .map((s) => `${s.name} - $${s.price} (${s.duration} minutes)`)
            .join('\n')

          response = `Here are some of our popular services:\n\n${serviceList}\n\nWould you like to book one of these or hear about other services?`
          suggestions = ['Book appointment', 'More services', 'Pricing info']
        }
      }

      // Booking intent handling
      else if (
        lowerMessage.includes('book') ||
        lowerMessage.includes('appointment') ||
        lowerMessage.includes('schedule')
      ) {
        nextAction = 'collect_info'

        // Try to extract service name from message
        const mentionedServices = await this.findServicesByName(storeId, message)
        if (mentionedServices.length > 0) {
          const service = mentionedServices[0]
          response = `Great! I can help you book ${service.name} for $${service.price}. When would you like to schedule this appointment?`
          suggestions = ['Today', 'Tomorrow', 'This week']
        } else {
          response = `I'd be happy to help you book an appointment! Which service are you interested in?\n\n${storeServices.services
            .slice(0, 3)
            .map((s) => s.name)
            .join(', ')}`
          suggestions = storeServices.services.slice(0, 3).map((s) => s.name)
        }
      }

      // Pricing inquiry
      else if (
        lowerMessage.includes('price') ||
        lowerMessage.includes('cost') ||
        lowerMessage.includes('how much')
      ) {
        if (storeServices.services.length > 0) {
          const priceList = storeServices.services
            .slice(0, 5)
            .map((s) => `${s.name}: $${s.price}`)
            .join('\n')

          response = `Here are our service prices:\n\n${priceList}\n\nWould you like to book any of these services?`
          suggestions = ['Book appointment', 'More info', 'Compare services']
        }
      }

      // Hours inquiry
      else if (
        lowerMessage.includes('hours') ||
        lowerMessage.includes('open') ||
        lowerMessage.includes('close')
      ) {
        if (store?.workingHour && Array.isArray(store.workingHour)) {
          const todaySchedule = store.workingHour.find(
            (wh) => wh.name?.toLowerCase() === DateTime.now().weekdayLong?.toLowerCase()
          )

          if (todaySchedule) {
            response = todaySchedule.isOpen
              ? `Today we're open from ${todaySchedule.from} to ${todaySchedule.to}. Would you like to book an appointment?`
              : `We're closed today. Would you like to book for another day?`
          } else {
            response = `Let me check our hours for you. Would you like to book an appointment for a specific day?`
          }
          suggestions = ['Book appointment', 'Check availability', 'Other days']
        }
      }

      // Goodbye handling
      else if (
        lowerMessage.includes('bye') ||
        lowerMessage.includes('goodbye') ||
        lowerMessage.includes('thank')
      ) {
        nextAction = 'end_conversation'
        response = `Thank you for calling ${storeName}! Have a wonderful day!`
      }

      // Transfer to human
      else if (
        lowerMessage.includes('help') ||
        lowerMessage.includes('human') ||
        lowerMessage.includes('agent') ||
        lowerMessage.includes('speak to someone')
      ) {
        nextAction = 'transfer_to_human'
        response = `I'll connect you with one of our team members right away. Please hold on.`
        suggestions = ['Wait for agent', 'Try again later']
      }

      return {
        response,
        nextAction,
        suggestions,
      }
    } catch (error) {
      console.error('Enhanced message processing error:', error)
      return {
        response:
          "I apologize, but I'm experiencing some technical difficulties. Please try again or speak with one of our team members.",
        nextAction: 'transfer_to_human',
        suggestions: ['Try again', 'Speak to agent'],
      }
    }
  }

  /**
   * Get store services with categories and pricing
   */
  async getStoreServices(storeId: string): Promise<{
    services: any[]
    categories: any[]
    packages: any[]
  }> {
    try {
      // Get services with categories
      const services = await ZnStoreService.query()
        .where('storeId', storeId)
        .preload('categories')
        .preload('image')
        .orderBy('name', 'asc')

      // Get categories
      const categories = await ZnStoreServiceCategory.query()
        .where('storeId', storeId)
        .orderBy('name', 'asc')

      // Get packages
      const packages = await ZnStorePackage.query()
        .where('storeId', storeId)
        .preload('services')
        .orderBy('name', 'asc')

      return {
        services: services.map((service) => ({
          id: service.id,
          name: service.name,
          price: service.price,
          duration: service.duration,
          categories: service.categories.map((cat) => cat.name),
        })),
        categories: categories.map((cat) => ({
          id: cat.id,
          name: cat.name,
        })),
        packages: packages.map((pkg) => ({
          id: pkg.id,
          name: pkg.name,
          // Packages don't have direct price, they use custom pricing per service
          services: pkg.services.map((svc) => svc.name),
        })),
      }
    } catch (error) {
      console.error('Error getting store services:', error)
      return { services: [], categories: [], packages: [] }
    }
  }

  /**
   * Check store availability for a given date/time
   */
  async checkAvailability(
    storeId: string,
    dateTime: DateTime,
    duration: number = 60
  ): Promise<{
    isAvailable: boolean
    suggestedTimes: DateTime[]
    reason?: string
  }> {
    try {
      const store = await ZnStore.find(storeId)
      if (!store) {
        return { isAvailable: false, suggestedTimes: [], reason: 'Store not found' }
      }

      // Check working hours
      const workingHours = store.workingHour
      if (workingHours && Array.isArray(workingHours)) {
        const dayOfWeek = dateTime.weekdayLong?.toLowerCase()
        const daySchedule = workingHours.find((wh) => wh.name?.toLowerCase() === dayOfWeek)

        if (!daySchedule || !daySchedule.isOpen) {
          return {
            isAvailable: false,
            suggestedTimes: await this.getSuggestedTimes(storeId, dateTime),
            reason: 'Store is closed on this day',
          }
        }

        // Check if time is within working hours
        const requestTime = dateTime.toFormat('HH:mm')
        if (daySchedule.from && daySchedule.to) {
          if (requestTime < daySchedule.from || requestTime > daySchedule.to) {
            return {
              isAvailable: false,
              suggestedTimes: await this.getSuggestedTimes(storeId, dateTime),
              reason: `Store is open from ${daySchedule.from} to ${daySchedule.to}`,
            }
          }
        }
      }

      // Check existing appointments
      const endTime = dateTime.plus({ minutes: duration })
      const existingAppointments = await this.appointmentService.getByStore(storeId, {
        startDate: dateTime.toJSDate(),
        endDate: endTime.toJSDate(),
        status: 'booked,confirmed,checked-in,in-service',
      })

      if (existingAppointments.length > 0) {
        return {
          isAvailable: false,
          suggestedTimes: await this.getSuggestedTimes(storeId, dateTime),
          reason: 'Time slot is already booked',
        }
      }

      return { isAvailable: true, suggestedTimes: [] }
    } catch (error) {
      console.error('Error checking availability:', error)
      return {
        isAvailable: false,
        suggestedTimes: [],
        reason: 'Error checking availability',
      }
    }
  }

  /**
   * Get suggested available times
   */
  private async getSuggestedTimes(storeId: string, requestedTime: DateTime): Promise<DateTime[]> {
    const suggestions: DateTime[] = []
    const baseDate = requestedTime.startOf('day')

    // Suggest times for the next 3 days
    for (let dayOffset = 0; dayOffset < 3; dayOffset++) {
      const checkDate = baseDate.plus({ days: dayOffset })

      // Suggest common appointment times (9 AM to 5 PM, every hour)
      for (let hour = 9; hour <= 17; hour++) {
        const suggestedTime = checkDate.set({ hour, minute: 0 })

        if (suggestedTime > DateTime.now()) {
          const availability = await this.checkAvailability(storeId, suggestedTime, 60)
          if (availability.isAvailable) {
            suggestions.push(suggestedTime)
            if (suggestions.length >= 3) break
          }
        }
      }

      if (suggestions.length >= 3) break
    }

    return suggestions
  }

  /**
   * Create or find customer from phone number
   */
  async handleCustomer(
    phoneNumber: string,
    storeId: string,
    additionalInfo?: { name?: string; email?: string }
  ): Promise<{ customer: any; isNewCustomer: boolean }> {
    try {
      // Clean phone number
      const cleanPhone = phoneNumber.replace(/\D/g, '')

      const result = await this.customerService.createCustomer(
        {
          phone: cleanPhone,
          firstName: additionalInfo?.name?.split(' ')[0],
          lastName: additionalInfo?.name?.split(' ').slice(1).join(' '),
          email: additionalInfo?.email,
        },
        storeId
      )

      return result
    } catch (error) {
      console.error('Error handling customer:', error)
      throw error
    }
  }

  /**
   * Create appointment with all business logic
   */
  async createAppointment(data: {
    storeId: string
    customerId: string
    serviceIds: string[]
    dateTime: DateTime
    duration?: number
    notes?: string
  }): Promise<any> {
    try {
      const { storeId, customerId, serviceIds, dateTime, duration, notes } = data

      // Calculate end time
      const totalDuration = duration || 60 // Default 1 hour
      const endTime = dateTime.plus({ minutes: totalDuration })

      // Create appointment
      const appointment = await this.appointmentService.create({
        storeId,
        customerId,
        startTime: dateTime.toJSDate(),
        endTime: endTime.toJSDate(),
        notes,
        services: serviceIds,
        packages: [], // No packages for now
      })

      return appointment
    } catch (error) {
      console.error('Error creating appointment:', error)
      throw error
    }
  }

  /**
   * Parse natural language date/time
   */
  parseDateTime(input: string, timezone: string = 'UTC'): DateTime | null {
    try {
      const now = DateTime.now().setZone(timezone)
      const lowerInput = input.toLowerCase()

      // Handle "today", "tomorrow", "next week" etc.
      if (lowerInput.includes('today')) {
        const timeMatch = input.match(/(\d{1,2}):?(\d{2})?\s*(am|pm)?/i)
        if (timeMatch) {
          let hour = parseInt(timeMatch[1])
          const minute = parseInt(timeMatch[2] || '0')
          const ampm = timeMatch[3]?.toLowerCase()

          if (ampm === 'pm' && hour !== 12) hour += 12
          if (ampm === 'am' && hour === 12) hour = 0

          return now.set({ hour, minute, second: 0, millisecond: 0 })
        }
      }

      if (lowerInput.includes('tomorrow')) {
        const timeMatch = input.match(/(\d{1,2}):?(\d{2})?\s*(am|pm)?/i)
        if (timeMatch) {
          let hour = parseInt(timeMatch[1])
          const minute = parseInt(timeMatch[2] || '0')
          const ampm = timeMatch[3]?.toLowerCase()

          if (ampm === 'pm' && hour !== 12) hour += 12
          if (ampm === 'am' && hour === 12) hour = 0

          return now.plus({ days: 1 }).set({ hour, minute, second: 0, millisecond: 0 })
        }
      }

      // Handle specific dates like "Monday", "next Monday"
      const dayNames = [
        'monday',
        'tuesday',
        'wednesday',
        'thursday',
        'friday',
        'saturday',
        'sunday',
      ]
      for (let i = 0; i < dayNames.length; i++) {
        if (lowerInput.includes(dayNames[i])) {
          const targetWeekday = (i + 1) as 1 | 2 | 3 | 4 | 5 | 6 | 7 // Luxon uses 1-7 for Monday-Sunday
          let targetDate = now.set({ weekday: targetWeekday })

          // If the day has passed this week, move to next week
          if (targetDate <= now) {
            targetDate = targetDate.plus({ weeks: 1 })
          }

          const timeMatch = input.match(/(\d{1,2}):?(\d{2})?\s*(am|pm)?/i)
          if (timeMatch) {
            let hour = parseInt(timeMatch[1])
            const minute = parseInt(timeMatch[2] || '0')
            const ampm = timeMatch[3]?.toLowerCase()

            if (ampm === 'pm' && hour !== 12) hour += 12
            if (ampm === 'am' && hour === 12) hour = 0

            return targetDate.set({ hour, minute, second: 0, millisecond: 0 })
          }
        }
      }

      // Try to parse as ISO date or other formats
      const parsed = DateTime.fromISO(input, { zone: timezone })
      if (parsed.isValid) return parsed

      return null
    } catch (error) {
      console.error('Error parsing date/time:', error)
      return null
    }
  }

  /**
   * Find services by name or category
   */
  async findServicesByName(storeId: string, searchTerm: string): Promise<any[]> {
    try {
      const services = await ZnStoreService.query()
        .where('storeId', storeId)
        .where((query) => {
          query.whereILike('name', `%${searchTerm}%`)
        })
        .preload('categories')
        .limit(5)

      return services.map((service) => ({
        id: service.id,
        name: service.name,
        price: service.price,
        duration: service.duration,
        categories: service.categories.map((cat) => cat.name),
      }))
    } catch (error) {
      console.error('Error finding services:', error)
      return []
    }
  }

  /**
   * Complete booking flow with session management
   */
  async processBookingFlow(
    sessionId: string,
    message: string,
    phoneNumber: string
  ): Promise<{
    response: string
    nextAction: ConversationResponse['nextAction']
    suggestions?: string[]
  }> {
    try {
      const session = await this.sessionService.getSession(sessionId)
      if (!session) {
        throw new Error('Session not found')
      }

      const { storeId } = session
      const lowerMessage = message.toLowerCase()

      // Get current conversation state
      const currentState = session.context.conversationState
      const pendingBooking = session.context.pendingBooking

      switch (currentState) {
        case 'service_selection':
          // Customer is selecting a service
          const services = await this.findServicesByName(storeId, message)
          if (services.length > 0) {
            const selectedService = services[0]

            await this.sessionService.updatePendingBooking(sessionId, {
              services: [selectedService.id],
              totalPrice: selectedService.price,
              duration: selectedService.duration,
            })

            await this.sessionService.updateConversationState(sessionId, 'datetime_selection')

            return {
              response: `Perfect! I've selected ${selectedService.name} for $${selectedService.price}. When would you like to schedule this appointment?`,
              nextAction: 'collect_info',
              suggestions: ['Today', 'Tomorrow', 'This week'],
            }
          } else {
            return {
              response: `I couldn't find that service. Let me show you our available services. Which one interests you?`,
              nextAction: 'collect_info',
              suggestions: await this.getServiceSuggestions(storeId),
            }
          }

        case 'datetime_selection':
          // Customer is selecting date/time
          const parsedDateTime = this.parseDateTime(message)
          if (parsedDateTime) {
            // Check availability
            const availability = await this.checkAvailability(
              storeId,
              parsedDateTime,
              pendingBooking?.duration || 60
            )

            if (availability.isAvailable) {
              await this.sessionService.updatePendingBooking(sessionId, {
                dateTime: parsedDateTime,
              })

              await this.sessionService.updateConversationState(
                sessionId,
                'customer_info_collection'
              )

              return {
                response: `Great! ${parsedDateTime.toFormat('EEEE, MMMM d')} at ${parsedDateTime.toFormat('h:mm a')} is available. May I have your name for the appointment?`,
                nextAction: 'collect_info',
                suggestions: ['Provide name', 'Change time'],
              }
            } else {
              const suggestedTimes = availability.suggestedTimes
                .slice(0, 3)
                .map((dt) => dt.toFormat('EEEE h:mm a'))

              return {
                response: `I'm sorry, that time isn't available. ${availability.reason || ''} Here are some available times:\n\n${suggestedTimes.join('\n')}\n\nWhich would work better for you?`,
                nextAction: 'collect_info',
                suggestions: suggestedTimes,
              }
            }
          } else {
            return {
              response: `I didn't understand that date/time. Could you please specify when you'd like your appointment? For example, "tomorrow at 2 PM" or "Monday at 10 AM".`,
              nextAction: 'collect_info',
              suggestions: ['Today', 'Tomorrow', 'This week'],
            }
          }

        case 'customer_info_collection':
          // Customer is providing their information
          const customerInfo = this.extractCustomerInfo(message)

          await this.sessionService.updatePendingBooking(sessionId, {
            customerInfo: {
              ...pendingBooking?.customerInfo,
              ...customerInfo,
              phone: phoneNumber,
            },
          })

          await this.sessionService.updateConversationState(sessionId, 'booking_confirmation')

          const booking = session.context.pendingBooking!
          const confirmationMessage = `Perfect! Let me confirm your appointment:

Service: ${await this.getServiceName(storeId, booking.services[0])}
Date & Time: ${booking.dateTime?.toFormat('EEEE, MMMM d')} at ${booking.dateTime?.toFormat('h:mm a')}
Duration: ${booking.duration} minutes
Price: $${booking.totalPrice}
Name: ${customerInfo.name || 'Not provided'}

Should I book this appointment for you?`

          return {
            response: confirmationMessage,
            nextAction: 'collect_info',
            suggestions: ['Yes, book it', 'Change details', 'Cancel'],
          }

        case 'booking_confirmation':
          // Customer is confirming the booking
          if (
            lowerMessage.includes('yes') ||
            lowerMessage.includes('book') ||
            lowerMessage.includes('confirm')
          ) {
            try {
              // Create customer
              const customerResult = await this.handleCustomer(
                phoneNumber,
                storeId,
                pendingBooking?.customerInfo
              )

              // Create appointment
              const appointment = await this.createAppointment({
                storeId,
                customerId: customerResult.customer.id,
                serviceIds: pendingBooking?.services || [],
                dateTime: pendingBooking?.dateTime!,
                duration: pendingBooking?.duration,
                notes: `Booked via AI assistant. Customer: ${pendingBooking?.customerInfo?.name || 'N/A'}`,
              })

              await this.sessionService.updateConversationState(sessionId, 'booking_complete')
              await this.sessionService.updateSessionStatus(sessionId, 'completed')

              return {
                response: `Excellent! Your appointment has been booked successfully.

Confirmation Details:
- Appointment ID: ${appointment.id}
- Service: ${await this.getServiceName(storeId, pendingBooking?.services?.[0] || '')}
- Date & Time: ${pendingBooking?.dateTime?.toFormat('EEEE, MMMM d')} at ${pendingBooking?.dateTime?.toFormat('h:mm a')}
- Total: $${pendingBooking?.totalPrice}

We'll see you then! Is there anything else I can help you with?`,
                nextAction: 'end_conversation',
                suggestions: ['Thank you', 'Ask question', 'Goodbye'],
              }
            } catch (error) {
              console.error('Booking creation error:', error)
              return {
                response: `I'm sorry, there was an issue creating your appointment. Please try again or speak with one of our team members.`,
                nextAction: 'transfer_to_human',
                suggestions: ['Try again', 'Speak to agent'],
              }
            }
          } else if (lowerMessage.includes('no') || lowerMessage.includes('cancel')) {
            await this.sessionService.updateConversationState(sessionId, 'intent_detection')
            return {
              response: `No problem! Would you like to modify the appointment details or start over?`,
              nextAction: 'continue',
              suggestions: ['Start over', 'Modify details', 'End call'],
            }
          } else {
            return {
              response: `Should I go ahead and book this appointment for you?`,
              nextAction: 'collect_info',
              suggestions: ['Yes, book it', 'No, cancel', 'Change details'],
            }
          }

        default:
          // Fallback to basic processing
          return await this.processMessageBasic(message, storeId)
      }
    } catch (error) {
      console.error('Booking flow error:', error)
      return {
        response: `I'm sorry, there was an issue processing your request. Let me transfer you to one of our team members.`,
        nextAction: 'transfer_to_human',
        suggestions: ['Speak to agent', 'Try again'],
      }
    }
  }

  /**
   * Extract customer information from message
   */
  private extractCustomerInfo(message: string): { name?: string; email?: string } {
    const info: { name?: string; email?: string } = {}

    // Simple name extraction (assumes first words are name)
    const words = message.trim().split(/\s+/)
    if (words.length >= 1 && words.length <= 4) {
      // Likely a name if 1-4 words and doesn't contain common phrases
      const commonPhrases = ['my', 'name', 'is', 'i', 'am', 'call', 'me']
      const isLikelyName = !words.some(
        (word) =>
          commonPhrases.includes(word.toLowerCase()) || word.includes('@') || /\d/.test(word)
      )

      if (isLikelyName) {
        info.name = words.join(' ')
      }
    }

    // Email extraction
    const emailMatch = message.match(/\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/)
    if (emailMatch) {
      info.email = emailMatch[0]
    }

    return info
  }

  /**
   * Get service suggestions for UI
   */
  private async getServiceSuggestions(storeId: string): Promise<string[]> {
    const services = await this.getStoreServices(storeId)
    return services.services.slice(0, 3).map((s) => s.name)
  }

  /**
   * Get service name by ID
   */
  private async getServiceName(storeId: string, serviceId: string): Promise<string> {
    try {
      const service = await ZnStoreService.query()
        .where('id', serviceId)
        .where('storeId', storeId)
        .first()

      return service?.name || 'Unknown Service'
    } catch (error) {
      return 'Unknown Service'
    }
  }

  sayAndListen(message: any) {
    if (message) {
      this.voiceResponse.say({ voice: 'Polly.Joanna' }, message)
    }

    this.voiceResponse.record({
      timeout: 2,
      maxLength: 5,
      action: this.baseRoute + 'process-flow',
      transcribe: false,
      playBeep: true,
    })

    return this.voiceResponse.toString()
  }

  async translateVoiceToText(recordingUrl: any) {
    const filePath = './data/user_audio.wav'
    await this.waitForTwilioAudio(recordingUrl, filePath)

    // 2. Transcribe using OpenAI Whisper
    const transcription = await this.openai.audio.transcriptions.create({
      file: fs.createReadStream(filePath),
      model: 'whisper-1',
    })

    return transcription.text
  }

  redialToAgent() {
    this.voiceResponse.say({ voice: 'Polly.Joanna' }, 'Connecting you to a Zurno representative.')
    this.voiceResponse.dial(process.env.SUPPORT_PHONE_NUMBER) // agent phone or Twilio SIP endpoint
    return this.voiceResponse.toString()
  }

  handleMakeAppointment() {
    this.voiceResponse.say({ voice: 'Polly.Joanna' }, 'Making appointment.')
    return this.voiceResponse.toString()
  }

  async handleReception(room: ZnCall, message: ZnCallMessage, userSaid: string) {
    try {
      // Use enhanced AI conversation processing
      const conversationRequest: ConversationRequest = {
        message: userSaid,
        storeId: room.toNumber, // Use phone number to find store
        customerPhone: room.fromNumber,
        sessionId: room.threadId,
        callId: room.id,
      }

      const aiResponse = await this.processConversation(conversationRequest)

      // Store the AI response
      message.reply = aiResponse.response
      await message.save()

      console.log('Enhanced AI answer:', aiResponse.response)
      console.log('Next action:', aiResponse.nextAction)

      return aiResponse
    } catch (error) {
      console.error('Enhanced reception handling error:', error)

      // Fallback to basic AI assistant
      const assistant = await ZnAIAssistant.findBy({ role: EAIAssistantRole.CUSTOMER_SERVICE })
      let answer = 'I dont have answer'
      if (assistant) {
        const parsed = await this.zurnoAssistanceService.getAIAnswer(
          assistant,
          room.threadId,
          userSaid
        )
        answer = parsed?.text || 'No answer'

        // Store new message
        message.reply = answer
        await message.save()
      }
      console.log('Fallback AI answer:', answer)

      return {
        response: answer,
        nextAction: 'continue',
        sessionId: room.threadId,
      }
    }
  }

  handleLoop() {
    this.voiceResponse.redirect(this.baseRoute + 'process-flow')
    return this.voiceResponse.toString()
  }

  handleBye() {
    this.voiceResponse.say({ voice: 'Polly.Joanna' }, 'Bye. Have a great day!')
    return this.voiceResponse.toString()
  }

  handlePause(userMessage: ZnCallMessage) {
    this.voiceResponse.say({ voice: 'Polly.Joanna' }, 'Please hold while we process your request.')
    this.voiceResponse.pause({ length: 5 })
    this.voiceResponse.redirect(this.baseRoute + 'process-reception/' + userMessage.id)
    return this.voiceResponse.toString()
  }
  async waitForTwilioAudio(url: string, savePath: fs.PathLike, maxAttempts = 5, delayMs = 1500) {
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        const response = await axios.get(url, { responseType: 'stream' })

        await new Promise((resolve, reject) => {
          const writer = fs.createWriteStream(savePath)
          response.data.pipe(writer)
          // @ts-ignore
          writer.on('finish', resolve)
          writer.on('error', reject)
        })

        return true // success
      } catch (err) {
        if (attempt === maxAttempts)
          throw new Error(`Failed to download Twilio file after ${maxAttempts} attempts`)
        if (err.response?.status === 404) {
          console.log(`Recording not ready (attempt ${attempt}), retrying in ${delayMs}ms...`)
          await new Promise((res) => setTimeout(res, delayMs))
        } else {
          throw err
        }
      }
    }
  }
}
