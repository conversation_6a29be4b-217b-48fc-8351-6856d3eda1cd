import { inject } from '@adonisjs/core'
import { DateTime } from 'luxon'
import Redis from 'ioredis'
import { BookingIntent, BookingEntity } from './booking_nlp_service.js'

export interface ConversationSession {
  sessionId: string
  storeId: string
  customerId?: string
  phoneNumber?: string
  status: SessionStatus
  currentIntent?: string
  context: SessionContext
  messages: ConversationMessage[]
  createdAt: DateTime
  updatedAt: DateTime
  expiresAt: DateTime
}

export interface SessionContext {
  collectedEntities: BookingEntity[]
  pendingBooking?: PendingBooking
  lastIntent?: BookingIntent
  conversationState: ConversationState
  retryCount: number
  clarificationNeeded: string[]
  userPreferences: UserPreferences
  metadata: Record<string, any>
}

export interface PendingBooking {
  services: string[]
  dateTime?: DateTime
  duration?: number
  staff?: string
  customerInfo?: CustomerInfo
  totalPrice?: number
  isConfirmed: boolean
  confirmationAttempts: number
}

export interface CustomerInfo {
  name?: string
  phone?: string
  email?: string
  isExisting?: boolean
  customerId?: string
}

export interface ConversationMessage {
  id: string
  type: 'user' | 'assistant' | 'system'
  content: string
  timestamp: DateTime
  intent?: string
  entities?: BookingEntity[]
  confidence?: number
}

export interface UserPreferences {
  preferredCommunicationStyle: 'formal' | 'casual' | 'auto'
  language: string
  timezone: string
  reminderPreferences: boolean
}

export type SessionStatus = 'active' | 'pending_confirmation' | 'completed' | 'expired' | 'cancelled'
export type ConversationState = 
  | 'greeting' 
  | 'intent_detection' 
  | 'entity_collection' 
  | 'service_selection' 
  | 'datetime_selection' 
  | 'customer_info_collection' 
  | 'booking_confirmation' 
  | 'booking_complete' 
  | 'error_recovery'

@inject()
export default class SessionManagementService {
  private redis: Redis
  private readonly SESSION_TTL = 3600 // 1 hour in seconds
  private readonly SESSION_PREFIX = 'booking_session:'

  constructor() {
    this.redis = new Redis({
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT || '6379'),
      password: process.env.REDIS_PASSWORD,
      db: parseInt(process.env.REDIS_DB || '0'),
    })
  }

  /**
   * Create a new conversation session
   */
  async createSession(
    storeId: string,
    phoneNumber?: string,
    customerId?: string
  ): Promise<ConversationSession> {
    const sessionId = this.generateSessionId()
    const now = DateTime.now()
    
    const session: ConversationSession = {
      sessionId,
      storeId,
      customerId,
      phoneNumber,
      status: 'active',
      context: {
        collectedEntities: [],
        conversationState: 'greeting',
        retryCount: 0,
        clarificationNeeded: [],
        userPreferences: {
          preferredCommunicationStyle: 'auto',
          language: 'en',
          timezone: 'UTC',
          reminderPreferences: true,
        },
        metadata: {},
      },
      messages: [],
      createdAt: now,
      updatedAt: now,
      expiresAt: now.plus({ seconds: this.SESSION_TTL }),
    }

    await this.saveSession(session)
    return session
  }

  /**
   * Get existing session by ID
   */
  async getSession(sessionId: string): Promise<ConversationSession | null> {
    try {
      const sessionData = await this.redis.get(this.getSessionKey(sessionId))
      if (!sessionData) return null

      const session = JSON.parse(sessionData) as ConversationSession
      
      // Convert date strings back to DateTime objects
      session.createdAt = DateTime.fromISO(session.createdAt as any)
      session.updatedAt = DateTime.fromISO(session.updatedAt as any)
      session.expiresAt = DateTime.fromISO(session.expiresAt as any)
      
      session.messages = session.messages.map(msg => ({
        ...msg,
        timestamp: DateTime.fromISO(msg.timestamp as any),
      }))

      if (session.context.pendingBooking?.dateTime) {
        session.context.pendingBooking.dateTime = DateTime.fromISO(
          session.context.pendingBooking.dateTime as any
        )
      }

      // Check if session is expired
      if (session.expiresAt < DateTime.now()) {
        await this.expireSession(sessionId)
        return null
      }

      return session
    } catch (error) {
      console.error('Error getting session:', error)
      return null
    }
  }

  /**
   * Update existing session
   */
  async updateSession(session: ConversationSession): Promise<void> {
    session.updatedAt = DateTime.now()
    await this.saveSession(session)
  }

  /**
   * Add message to session
   */
  async addMessage(
    sessionId: string,
    type: ConversationMessage['type'],
    content: string,
    intent?: string,
    entities?: BookingEntity[],
    confidence?: number
  ): Promise<void> {
    const session = await this.getSession(sessionId)
    if (!session) throw new Error('Session not found')

    const message: ConversationMessage = {
      id: this.generateMessageId(),
      type,
      content,
      timestamp: DateTime.now(),
      intent,
      entities,
      confidence,
    }

    session.messages.push(message)
    
    // Keep only last 50 messages to prevent memory issues
    if (session.messages.length > 50) {
      session.messages = session.messages.slice(-50)
    }

    await this.updateSession(session)
  }

  /**
   * Update conversation state
   */
  async updateConversationState(
    sessionId: string,
    state: ConversationState
  ): Promise<void> {
    const session = await this.getSession(sessionId)
    if (!session) throw new Error('Session not found')

    session.context.conversationState = state
    await this.updateSession(session)
  }

  /**
   * Add entities to session context
   */
  async addEntities(
    sessionId: string,
    entities: BookingEntity[]
  ): Promise<void> {
    const session = await this.getSession(sessionId)
    if (!session) throw new Error('Session not found')

    // Merge new entities with existing ones, avoiding duplicates
    for (const entity of entities) {
      const existingIndex = session.context.collectedEntities.findIndex(
        e => e.type === entity.type && e.value === entity.value
      )
      
      if (existingIndex >= 0) {
        // Update existing entity if new one has higher confidence
        if (entity.confidence > session.context.collectedEntities[existingIndex].confidence) {
          session.context.collectedEntities[existingIndex] = entity
        }
      } else {
        session.context.collectedEntities.push(entity)
      }
    }

    await this.updateSession(session)
  }

  /**
   * Update pending booking information
   */
  async updatePendingBooking(
    sessionId: string,
    bookingData: Partial<PendingBooking>
  ): Promise<void> {
    const session = await this.getSession(sessionId)
    if (!session) throw new Error('Session not found')

    if (!session.context.pendingBooking) {
      session.context.pendingBooking = {
        services: [],
        isConfirmed: false,
        confirmationAttempts: 0,
      }
    }

    Object.assign(session.context.pendingBooking, bookingData)
    await this.updateSession(session)
  }

  /**
   * Set clarification needed
   */
  async setClarificationNeeded(
    sessionId: string,
    clarifications: string[]
  ): Promise<void> {
    const session = await this.getSession(sessionId)
    if (!session) throw new Error('Session not found')

    session.context.clarificationNeeded = clarifications
    await this.updateSession(session)
  }

  /**
   * Clear clarification needed
   */
  async clearClarificationNeeded(sessionId: string): Promise<void> {
    const session = await this.getSession(sessionId)
    if (!session) throw new Error('Session not found')

    session.context.clarificationNeeded = []
    await this.updateSession(session)
  }

  /**
   * Increment retry count
   */
  async incrementRetryCount(sessionId: string): Promise<number> {
    const session = await this.getSession(sessionId)
    if (!session) throw new Error('Session not found')

    session.context.retryCount += 1
    await this.updateSession(session)
    return session.context.retryCount
  }

  /**
   * Reset retry count
   */
  async resetRetryCount(sessionId: string): Promise<void> {
    const session = await this.getSession(sessionId)
    if (!session) throw new Error('Session not found')

    session.context.retryCount = 0
    await this.updateSession(session)
  }

  /**
   * Update session status
   */
  async updateSessionStatus(
    sessionId: string,
    status: SessionStatus
  ): Promise<void> {
    const session = await this.getSession(sessionId)
    if (!session) throw new Error('Session not found')

    session.status = status
    await this.updateSession(session)
  }

  /**
   * Extend session expiration
   */
  async extendSession(
    sessionId: string,
    additionalSeconds: number = this.SESSION_TTL
  ): Promise<void> {
    const session = await this.getSession(sessionId)
    if (!session) throw new Error('Session not found')

    session.expiresAt = DateTime.now().plus({ seconds: additionalSeconds })
    await this.updateSession(session)
  }

  /**
   * Expire session
   */
  async expireSession(sessionId: string): Promise<void> {
    await this.redis.del(this.getSessionKey(sessionId))
  }

  /**
   * Get session by phone number (for callback scenarios)
   */
  async getSessionByPhone(
    phoneNumber: string,
    storeId: string
  ): Promise<ConversationSession | null> {
    try {
      // This is a simplified implementation
      // In production, you might want to maintain a separate index
      const keys = await this.redis.keys(`${this.SESSION_PREFIX}*`)
      
      for (const key of keys) {
        const sessionData = await this.redis.get(key)
        if (sessionData) {
          const session = JSON.parse(sessionData) as ConversationSession
          if (session.phoneNumber === phoneNumber && session.storeId === storeId) {
            return await this.getSession(session.sessionId)
          }
        }
      }
      
      return null
    } catch (error) {
      console.error('Error getting session by phone:', error)
      return null
    }
  }

  /**
   * Get active sessions for a store
   */
  async getActiveSessionsForStore(storeId: string): Promise<ConversationSession[]> {
    try {
      const keys = await this.redis.keys(`${this.SESSION_PREFIX}*`)
      const sessions: ConversationSession[] = []
      
      for (const key of keys) {
        const sessionData = await this.redis.get(key)
        if (sessionData) {
          const session = JSON.parse(sessionData) as ConversationSession
          if (session.storeId === storeId && session.status === 'active') {
            const fullSession = await this.getSession(session.sessionId)
            if (fullSession) {
              sessions.push(fullSession)
            }
          }
        }
      }
      
      return sessions
    } catch (error) {
      console.error('Error getting active sessions:', error)
      return []
    }
  }

  /**
   * Clean up expired sessions
   */
  async cleanupExpiredSessions(): Promise<number> {
    try {
      const keys = await this.redis.keys(`${this.SESSION_PREFIX}*`)
      let cleanedCount = 0
      
      for (const key of keys) {
        const sessionData = await this.redis.get(key)
        if (sessionData) {
          const session = JSON.parse(sessionData) as ConversationSession
          const expiresAt = DateTime.fromISO(session.expiresAt as any)
          
          if (expiresAt < DateTime.now()) {
            await this.redis.del(key)
            cleanedCount++
          }
        }
      }
      
      return cleanedCount
    } catch (error) {
      console.error('Error cleaning up expired sessions:', error)
      return 0
    }
  }

  /**
   * Get conversation history
   */
  async getConversationHistory(
    sessionId: string,
    limit: number = 20
  ): Promise<ConversationMessage[]> {
    const session = await this.getSession(sessionId)
    if (!session) return []

    return session.messages.slice(-limit)
  }

  /**
   * Update user preferences
   */
  async updateUserPreferences(
    sessionId: string,
    preferences: Partial<UserPreferences>
  ): Promise<void> {
    const session = await this.getSession(sessionId)
    if (!session) throw new Error('Session not found')

    Object.assign(session.context.userPreferences, preferences)
    await this.updateSession(session)
  }

  /**
   * Add metadata to session
   */
  async addMetadata(
    sessionId: string,
    key: string,
    value: any
  ): Promise<void> {
    const session = await this.getSession(sessionId)
    if (!session) throw new Error('Session not found')

    session.context.metadata[key] = value
    await this.updateSession(session)
  }

  /**
   * Get session statistics
   */
  async getSessionStats(sessionId: string): Promise<{
    messageCount: number
    duration: number
    entitiesCollected: number
    retryCount: number
    currentState: ConversationState
  }> {
    const session = await this.getSession(sessionId)
    if (!session) {
      throw new Error('Session not found')
    }

    const duration = session.updatedAt.diff(session.createdAt, 'seconds').seconds

    return {
      messageCount: session.messages.length,
      duration,
      entitiesCollected: session.context.collectedEntities.length,
      retryCount: session.context.retryCount,
      currentState: session.context.conversationState,
    }
  }

  /**
   * Private helper methods
   */
  private async saveSession(session: ConversationSession): Promise<void> {
    const key = this.getSessionKey(session.sessionId)
    const ttl = Math.max(1, session.expiresAt.diff(DateTime.now(), 'seconds').seconds)
    
    await this.redis.setex(key, ttl, JSON.stringify(session))
  }

  private getSessionKey(sessionId: string): string {
    return `${this.SESSION_PREFIX}${sessionId}`
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }
}