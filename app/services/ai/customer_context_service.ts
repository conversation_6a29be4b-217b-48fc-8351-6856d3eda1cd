import { inject } from '@adonisjs/core'
import ZnUser from '#models/zn_user'
import ZnAppointment from '#models/store_service/zn_appointment'
import CacheService from '#services/cache_service'
import { DateTime } from 'luxon'

export interface CustomerContext {
  customer: {
    id: string
    name: string
    email: string
    phoneNumber: string
    preferences: CustomerPreferences
    loyaltyStatus: LoyaltyStatus
  }
  appointmentHistory: AppointmentHistory[]
  preferences: {
    preferredServices: string[]
    preferredStaff: string[]
    preferredTimes: TimePreference[]
    communicationPreferences: CommunicationPreference[]
  }
  insights: CustomerInsights
}

export interface CustomerPreferences {
  preferredServices: string[]
  preferredStaff: string[]
  preferredTimes: string[]
  specialRequests: string[]
  allergies: string[]
  notes: string
}

export interface LoyaltyStatus {
  tier: 'bronze' | 'silver' | 'gold' | 'platinum'
  points: number
  totalSpent: number
  memberSince: DateTime
  benefits: string[]
}

export interface AppointmentHistory {
  id: string
  date: DateTime
  services: string[]
  staff: string[]
  total: number
  status: string
  rating?: number
  feedback?: string
}

export interface TimePreference {
  dayOfWeek: string
  timeRange: string
  frequency: number
}

export interface CommunicationPreference {
  type: 'email' | 'sms' | 'phone' | 'app'
  enabled: boolean
  timing: string[]
}

export interface CustomerInsights {
  visitFrequency: 'first-time' | 'occasional' | 'regular' | 'frequent'
  averageSpending: number
  favoriteServices: string[]
  lastVisit?: DateTime
  nextSuggestedVisit?: DateTime
  riskLevel: 'low' | 'medium' | 'high' // churn risk
  personalizedRecommendations: string[]
}

@inject()
export default class CustomerContextService {
  constructor(private cacheService: CacheService) {}

  /**
   * Load comprehensive customer context for AI conversations
   */
  async loadCustomerContext(customerId: string, storeId: string): Promise<CustomerContext> {
    const cacheKey = `customer_context:${customerId}:${storeId}`
    
    // Try to get from cache first
    const cached = await this.cacheService.get(cacheKey)
    if (cached) {
      return JSON.parse(cached)
    }

    // Load fresh data
    const context = await this.buildCustomerContext(customerId, storeId)
    
    // Cache for 15 minutes
    await this.cacheService.set(cacheKey, JSON.stringify(context), 900)
    
    return context
  }

  /**
   * Build customer context from database
   */
  private async buildCustomerContext(customerId: string, storeId: string): Promise<CustomerContext> {
    const customer = await ZnUser.query()
      .where('id', customerId)
      .firstOrFail()

    const appointmentHistory = await this.loadAppointmentHistory(customerId, storeId)
    const preferences = await this.loadCustomerPreferences(customerId, storeId)
    const insights = await this.generateCustomerInsights(customerId, storeId, appointmentHistory)

    return {
      customer: {
        id: customer.id,
        name: `${customer.firstName} ${customer.lastName}`,
        email: customer.email,
        phoneNumber: customer.phoneNumber || '',
        preferences: await this.loadDetailedPreferences(customerId),
        loyaltyStatus: await this.calculateLoyaltyStatus(customerId, storeId),
      },
      appointmentHistory,
      preferences,
      insights,
    }
  }

  /**
   * Load customer appointment history
   */
  private async loadAppointmentHistory(customerId: string, storeId: string): Promise<AppointmentHistory[]> {
    const appointments = await ZnAppointment.query()
      .where('customerId', customerId)
      .where('storeId', storeId)
      .preload('services')
      .orderBy('startTime', 'desc')
      .limit(20) // Last 20 appointments

    return appointments.map(apt => ({
      id: apt.id,
      date: apt.startTime || DateTime.now(),
      services: apt.services?.map(s => s.name) || [],
      staff: [], // TODO: Add staff relationship to appointment model
      total: apt.total,
      status: apt.status,
      rating: undefined, // TODO: Add rating system
      feedback: apt.notes,
    }))
  }

  /**
   * Load customer preferences
   */
  private async loadCustomerPreferences(customerId: string, storeId: string): Promise<{
    preferredServices: string[]
    preferredStaff: string[]
    preferredTimes: TimePreference[]
    communicationPreferences: CommunicationPreference[]
  }> {
    // This would typically come from a customer preferences table
    // For now, derive from appointment history
    const history = await this.loadAppointmentHistory(customerId, storeId)
    
    const serviceFrequency = new Map<string, number>()
    const timeFrequency = new Map<string, number>()
    
    history.forEach(apt => {
      apt.services.forEach(service => {
        serviceFrequency.set(service, (serviceFrequency.get(service) || 0) + 1)
      })
      
      if (apt.date) {
        const timeSlot = `${apt.date.toFormat('cccc')}-${apt.date.hour}`
        timeFrequency.set(timeSlot, (timeFrequency.get(timeSlot) || 0) + 1)
      }
    })

    const preferredServices = Array.from(serviceFrequency.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5)
      .map(([service]) => service)

    const preferredTimes = Array.from(timeFrequency.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 3)
      .map(([timeSlot, frequency]) => {
        const [dayOfWeek, hour] = timeSlot.split('-')
        return {
          dayOfWeek,
          timeRange: `${hour}:00-${parseInt(hour) + 1}:00`,
          frequency,
        }
      })

    return {
      preferredServices,
      preferredStaff: [], // TODO: Implement when staff tracking is available
      preferredTimes,
      communicationPreferences: [
        { type: 'email', enabled: true, timing: ['booking_confirmation', 'reminder'] },
        { type: 'sms', enabled: false, timing: ['reminder'] },
      ],
    }
  }

  /**
   * Load detailed customer preferences
   */
  private async loadDetailedPreferences(customerId: string): Promise<CustomerPreferences> {
    // This would come from a customer preferences table
    // For now, return default structure
    return {
      preferredServices: [],
      preferredStaff: [],
      preferredTimes: [],
      specialRequests: [],
      allergies: [],
      notes: '',
    }
  }

  /**
   * Calculate customer loyalty status
   */
  private async calculateLoyaltyStatus(customerId: string, storeId: string): Promise<LoyaltyStatus> {
    const appointments = await ZnAppointment.query()
      .where('customerId', customerId)
      .where('storeId', storeId)
      .where('status', '!=', 'cancelled')

    const totalSpent = appointments.reduce((sum, apt) => sum + apt.total, 0)
    const appointmentCount = appointments.length
    
    let tier: 'bronze' | 'silver' | 'gold' | 'platinum' = 'bronze'
    let points = Math.floor(totalSpent / 10) // 1 point per $10 spent
    
    if (totalSpent >= 2000 || appointmentCount >= 20) {
      tier = 'platinum'
    } else if (totalSpent >= 1000 || appointmentCount >= 12) {
      tier = 'gold'
    } else if (totalSpent >= 500 || appointmentCount >= 6) {
      tier = 'silver'
    }

    const benefits = this.getTierBenefits(tier)
    const memberSince = appointments.length > 0 
      ? appointments[appointments.length - 1].createdAt 
      : DateTime.now()

    return {
      tier,
      points,
      totalSpent,
      memberSince,
      benefits,
    }
  }

  /**
   * Get benefits for loyalty tier
   */
  private getTierBenefits(tier: string): string[] {
    const benefits = {
      bronze: ['Birthday discount', 'Appointment reminders'],
      silver: ['5% discount on services', 'Priority booking', 'Birthday discount'],
      gold: ['10% discount on services', 'Priority booking', 'Free add-ons', 'Birthday discount'],
      platinum: ['15% discount on services', 'VIP priority booking', 'Free services', 'Exclusive events'],
    }
    
    return benefits[tier as keyof typeof benefits] || []
  }

  /**
   * Generate customer insights
   */
  private async generateCustomerInsights(
    customerId: string, 
    storeId: string, 
    history: AppointmentHistory[]
  ): Promise<CustomerInsights> {
    const now = DateTime.now()
    const lastVisit = history.length > 0 ? history[0].date : undefined
    const averageSpending = history.length > 0 
      ? history.reduce((sum, apt) => sum + apt.total, 0) / history.length 
      : 0

    // Calculate visit frequency
    let visitFrequency: 'first-time' | 'occasional' | 'regular' | 'frequent' = 'first-time'
    if (history.length >= 12) {
      visitFrequency = 'frequent'
    } else if (history.length >= 6) {
      visitFrequency = 'regular'
    } else if (history.length >= 2) {
      visitFrequency = 'occasional'
    }

    // Calculate favorite services
    const serviceFrequency = new Map<string, number>()
    history.forEach(apt => {
      apt.services.forEach(service => {
        serviceFrequency.set(service, (serviceFrequency.get(service) || 0) + 1)
      })
    })
    
    const favoriteServices = Array.from(serviceFrequency.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 3)
      .map(([service]) => service)

    // Calculate churn risk
    let riskLevel: 'low' | 'medium' | 'high' = 'low'
    if (lastVisit) {
      const daysSinceLastVisit = now.diff(lastVisit, 'days').days
      if (daysSinceLastVisit > 90) {
        riskLevel = 'high'
      } else if (daysSinceLastVisit > 45) {
        riskLevel = 'medium'
      }
    }

    // Generate recommendations
    const recommendations = this.generateRecommendations(history, favoriteServices, visitFrequency)

    return {
      visitFrequency,
      averageSpending,
      favoriteServices,
      lastVisit,
      nextSuggestedVisit: this.calculateNextSuggestedVisit(history),
      riskLevel,
      personalizedRecommendations: recommendations,
    }
  }

  /**
   * Generate personalized recommendations
   */
  private generateRecommendations(
    history: AppointmentHistory[], 
    favoriteServices: string[], 
    visitFrequency: string
  ): string[] {
    const recommendations: string[] = []

    if (favoriteServices.length > 0) {
      recommendations.push(`Consider booking your favorite service: ${favoriteServices[0]}`)
    }

    if (visitFrequency === 'frequent') {
      recommendations.push('You might enjoy our new premium package deals')
    } else if (visitFrequency === 'first-time') {
      recommendations.push('Welcome! Try our popular introductory package')
    }

    if (history.length > 0) {
      const lastServices = history[0].services
      if (lastServices.includes('Manicure') && !lastServices.includes('Pedicure')) {
        recommendations.push('Complete your look with a matching pedicure')
      }
    }

    return recommendations
  }

  /**
   * Calculate next suggested visit date
   */
  private calculateNextSuggestedVisit(history: AppointmentHistory[]): DateTime | undefined {
    if (history.length < 2) return undefined

    // Calculate average time between visits
    const intervals: number[] = []
    for (let i = 0; i < history.length - 1; i++) {
      const interval = history[i].date.diff(history[i + 1].date, 'days').days
      intervals.push(interval)
    }

    const averageInterval = intervals.reduce((sum, interval) => sum + interval, 0) / intervals.length
    const lastVisit = history[0].date
    
    return lastVisit.plus({ days: Math.round(averageInterval) })
  }

  /**
   * Get customer context summary for AI prompts
   */
  async getContextSummary(customerId: string, storeId: string): Promise<string> {
    const context = await this.loadCustomerContext(customerId, storeId)
    
    const favoriteServices = context.insights.favoriteServices.join(', ')
    const loyaltyInfo = `${context.customer.loyaltyStatus.tier} member with ${context.customer.loyaltyStatus.points} points`
    const visitInfo = `${context.insights.visitFrequency} visitor, last visit: ${context.insights.lastVisit?.toFormat('MMM dd, yyyy') || 'Never'}`
    
    return `Customer: ${context.customer.name}\nEmail: ${context.customer.email}\nLoyalty: ${loyaltyInfo}\nVisit Pattern: ${visitInfo}\nFavorite Services: ${favoriteServices}\nRecommendations: ${context.insights.personalizedRecommendations.join('; ')}`
  }

  /**
   * Invalidate customer context cache
   */
  async invalidateCache(customerId: string, storeId: string): Promise<void> {
    const cacheKey = `customer_context:${customerId}:${storeId}`
    await this.cacheService.delete(cacheKey)
  }

  /**
   * Update customer preferences
   */
  async updatePreferences(customerId: string, preferences: Partial<CustomerPreferences>): Promise<void> {
    // TODO: Implement when customer preferences table is available
    // For now, just invalidate cache
    const cachePattern = `customer_context:${customerId}:*`
    // Note: This would need a cache service method to delete by pattern
  }

  /**
   * Check if customer is VIP
   */
  async isVIPCustomer(customerId: string, storeId: string): Promise<boolean> {
    const context = await this.loadCustomerContext(customerId, storeId)
    return context.customer.loyaltyStatus.tier === 'platinum' || 
           context.customer.loyaltyStatus.totalSpent >= 2000
  }

  /**
   * Get customer's preferred booking times
   */
  async getPreferredTimes(customerId: string, storeId: string): Promise<TimePreference[]> {
    const context = await this.loadCustomerContext(customerId, storeId)
    return context.preferences.preferredTimes
  }
}