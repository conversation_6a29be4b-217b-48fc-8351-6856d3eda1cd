import { inject } from '@adonisjs/core'
import { DateTime } from 'luxon'
import ZnAppointment from '#models/zn_appointment'
import ZnStore from '#models/zn_store'
import ZnUser from '#models/zn_user'
import ZnStoreService from '#models/zn_store_service'
import ZnStorePackage from '#models/zn_store_package'
import AppointmentService from '#services/store_service/appointment_service'
import StoreContextService, { ServiceInfo } from './store_context_service.js'
import CustomerContextService from './customer_context_service.js'
import { PendingBooking, CustomerInfo } from './session_management_service.js'

export interface BookingRequest {
  storeId: string
  customerId?: string
  customerInfo?: CustomerInfo
  services: string[]
  packages?: string[]
  dateTime: DateTime
  duration?: number
  staffId?: string
  notes?: string
  source: 'ai_receptionist' | 'phone' | 'web' | 'mobile'
}

export interface BookingValidation {
  isValid: boolean
  errors: string[]
  warnings: string[]
  suggestions: string[]
}

export interface AvailabilitySlot {
  startTime: DateTime
  endTime: DateTime
  isAvailable: boolean
  staffId?: string
  conflictReason?: string
}

export interface BookingConfirmation {
  appointmentId: string
  confirmationNumber: string
  appointment: ZnAppointment
  totalPrice: number
  estimatedDuration: number
  nextSteps: string[]
}

export interface TimeSlot {
  time: DateTime
  available: boolean
  reason?: string
}

@inject()
export default class AppointmentBookingService {
  constructor(
    private appointmentService: AppointmentService,
    private storeContextService: StoreContextService,
    private customerContextService: CustomerContextService
  ) {}

  /**
   * Create appointment from AI booking request
   */
  async createAppointment(request: BookingRequest): Promise<BookingConfirmation> {
    // Validate the booking request
    const validation = await this.validateBookingRequest(request)
    if (!validation.isValid) {
      throw new Error(`Booking validation failed: ${validation.errors.join(', ')}`)
    }

    // Get or create customer
    const customer = await this.getOrCreateCustomer(request)
    
    // Get services and packages
    const services = await this.getServicesById(request.services)
    const packages = request.packages ? await this.getPackagesById(request.packages) : []
    
    // Calculate pricing
    const pricing = await this.calculatePricing(services, packages, request.storeId)
    
    // Create the appointment
    const appointmentData = {
      storeId: request.storeId,
      customerId: customer.id,
      startTime: request.dateTime,
      endTime: request.dateTime.plus({ minutes: request.duration || pricing.estimatedDuration }),
      notes: request.notes || `Booked via AI Receptionist - ${request.source}`,
      status: 'confirmed' as const,
      subtotal: pricing.subtotal,
      taxAmount: pricing.taxAmount,
      total: pricing.total,
      services: services.map(s => s.id),
      packages: packages.map(p => p.id),
    }

    const appointment = await this.appointmentService.create(appointmentData)
    
    // Generate confirmation
    const confirmation: BookingConfirmation = {
      appointmentId: appointment.id,
      confirmationNumber: this.generateConfirmationNumber(),
      appointment,
      totalPrice: pricing.total,
      estimatedDuration: pricing.estimatedDuration,
      nextSteps: await this.generateNextSteps(appointment),
    }

    return confirmation
  }

  /**
   * Validate booking request
   */
  async validateBookingRequest(request: BookingRequest): Promise<BookingValidation> {
    const errors: string[] = []
    const warnings: string[] = []
    const suggestions: string[] = []

    // Validate store exists and is active
    const store = await ZnStore.find(request.storeId)
    if (!store) {
      errors.push('Store not found')
      return { isValid: false, errors, warnings, suggestions }
    }

    // Validate services exist
    if (!request.services || request.services.length === 0) {
      errors.push('At least one service must be selected')
    } else {
      const services = await this.getServicesById(request.services)
      if (services.length !== request.services.length) {
        errors.push('One or more selected services not found')
      }
    }

    // Validate date/time
    if (!request.dateTime || !request.dateTime.isValid) {
      errors.push('Valid date and time required')
    } else {
      // Check if date is in the past
      if (request.dateTime < DateTime.now()) {
        errors.push('Cannot book appointments in the past')
      }
      
      // Check if date is too far in the future (e.g., 6 months)
      if (request.dateTime > DateTime.now().plus({ months: 6 })) {
        warnings.push('Booking is quite far in the future')
      }
      
      // Check store hours
      const isWithinHours = await this.isWithinStoreHours(request.storeId, request.dateTime)
      if (!isWithinHours) {
        errors.push('Selected time is outside store operating hours')
        const nextAvailable = await this.getNextAvailableTime(request.storeId, request.dateTime)
        if (nextAvailable) {
          suggestions.push(`Next available time: ${nextAvailable.toFormat('MMM dd, yyyy h:mm a')}`)
        }
      }
    }

    // Validate customer information
    if (!request.customerId && !request.customerInfo) {
      errors.push('Customer ID or customer information required')
    } else if (request.customerInfo) {
      if (!request.customerInfo.phone && !request.customerInfo.email) {
        errors.push('Customer phone number or email required')
      }
    }

    // Check availability
    if (request.dateTime && request.dateTime.isValid) {
      const isAvailable = await this.checkAvailability(
        request.storeId,
        request.dateTime,
        request.duration || 60,
        request.staffId
      )
      
      if (!isAvailable.isAvailable) {
        errors.push(`Time slot not available: ${isAvailable.conflictReason}`)
        
        // Suggest alternative times
        const alternatives = await this.suggestAlternativeTimes(
          request.storeId,
          request.dateTime,
          request.duration || 60
        )
        
        if (alternatives.length > 0) {
          suggestions.push(
            `Alternative times: ${alternatives.slice(0, 3).map(t => 
              t.toFormat('h:mm a')
            ).join(', ')}`
          )
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      suggestions,
    }
  }

  /**
   * Check availability for a specific time slot
   */
  async checkAvailability(
    storeId: string,
    dateTime: DateTime,
    duration: number,
    staffId?: string
  ): Promise<AvailabilitySlot> {
    const endTime = dateTime.plus({ minutes: duration })
    
    // Check store hours
    const isWithinHours = await this.isWithinStoreHours(storeId, dateTime)
    if (!isWithinHours) {
      return {
        startTime: dateTime,
        endTime,
        isAvailable: false,
        conflictReason: 'Outside store operating hours',
      }
    }

    // Check for existing appointments
    const conflictingAppointments = await ZnAppointment.query()
      .where('storeId', storeId)
      .where('status', '!=', 'cancelled')
      .where((query) => {
        query
          .whereBetween('startTime', [dateTime.toJSDate(), endTime.toJSDate()])
          .orWhereBetween('endTime', [dateTime.toJSDate(), endTime.toJSDate()])
          .orWhere((subQuery) => {
            subQuery
              .where('startTime', '<=', dateTime.toJSDate())
              .where('endTime', '>=', endTime.toJSDate())
          })
      })
    
    if (staffId) {
      conflictingAppointments.where('staffId', staffId)
    }
    
    const conflicts = await conflictingAppointments.exec()
    
    if (conflicts.length > 0) {
      return {
        startTime: dateTime,
        endTime,
        isAvailable: false,
        staffId,
        conflictReason: 'Time slot already booked',
      }
    }

    return {
      startTime: dateTime,
      endTime,
      isAvailable: true,
      staffId,
    }
  }

  /**
   * Get available time slots for a specific date
   */
  async getAvailableTimeSlots(
    storeId: string,
    date: DateTime,
    duration: number = 60,
    staffId?: string
  ): Promise<TimeSlot[]> {
    const slots: TimeSlot[] = []
    const storeContext = await this.storeContextService.loadStoreContext(storeId)
    
    // Get store hours for the specific day
    const dayOfWeek = date.weekdayLong?.toLowerCase()
    const storeHours = storeContext.workingHours.find(h => h.day === dayOfWeek)
    
    if (!storeHours || !storeHours.isOpen) {
      return slots
    }

    // Generate time slots every 30 minutes
    const startTime = date.set({
      hour: parseInt(storeHours.openTime.split(':')[0]),
      minute: parseInt(storeHours.openTime.split(':')[1]),
    })
    
    const endTime = date.set({
      hour: parseInt(storeHours.closeTime.split(':')[0]),
      minute: parseInt(storeHours.closeTime.split(':')[1]),
    })

    let currentTime = startTime
    while (currentTime.plus({ minutes: duration }) <= endTime) {
      const availability = await this.checkAvailability(
        storeId,
        currentTime,
        duration,
        staffId
      )
      
      slots.push({
        time: currentTime,
        available: availability.isAvailable,
        reason: availability.conflictReason,
      })
      
      currentTime = currentTime.plus({ minutes: 30 })
    }

    return slots
  }

  /**
   * Suggest alternative times when requested time is not available
   */
  async suggestAlternativeTimes(
    storeId: string,
    requestedTime: DateTime,
    duration: number = 60,
    maxSuggestions: number = 5
  ): Promise<DateTime[]> {
    const suggestions: DateTime[] = []
    const searchDate = requestedTime.startOf('day')
    
    // Search within the same day first
    const sameDaySlots = await this.getAvailableTimeSlots(storeId, searchDate, duration)
    const availableSameDay = sameDaySlots
      .filter(slot => slot.available && slot.time > DateTime.now())
      .map(slot => slot.time)
      .slice(0, maxSuggestions)
    
    suggestions.push(...availableSameDay)
    
    // If not enough suggestions, search next few days
    if (suggestions.length < maxSuggestions) {
      for (let i = 1; i <= 7 && suggestions.length < maxSuggestions; i++) {
        const nextDate = searchDate.plus({ days: i })
        const nextDaySlots = await this.getAvailableTimeSlots(storeId, nextDate, duration)
        
        const availableNextDay = nextDaySlots
          .filter(slot => slot.available)
          .map(slot => slot.time)
          .slice(0, maxSuggestions - suggestions.length)
        
        suggestions.push(...availableNextDay)
      }
    }

    return suggestions.slice(0, maxSuggestions)
  }

  /**
   * Convert pending booking to booking request
   */
  convertPendingBookingToRequest(
    pendingBooking: PendingBooking,
    storeId: string,
    source: BookingRequest['source'] = 'ai_receptionist'
  ): BookingRequest {
    if (!pendingBooking.dateTime) {
      throw new Error('Date and time required for booking')
    }

    return {
      storeId,
      customerInfo: pendingBooking.customerInfo,
      services: pendingBooking.services,
      dateTime: pendingBooking.dateTime,
      duration: pendingBooking.duration,
      staffId: pendingBooking.staff,
      source,
    }
  }

  /**
   * Get or create customer from customer info
   */
  private async getOrCreateCustomer(request: BookingRequest): Promise<ZnUser> {
    if (request.customerId) {
      const customer = await ZnUser.find(request.customerId)
      if (customer) return customer
    }

    if (!request.customerInfo) {
      throw new Error('Customer information required')
    }

    // Try to find existing customer by phone or email
    let customer = null
    
    if (request.customerInfo.phone) {
      customer = await ZnUser.query()
        .where('phoneNumber', request.customerInfo.phone)
        .first()
    }
    
    if (!customer && request.customerInfo.email) {
      customer = await ZnUser.query()
        .where('email', request.customerInfo.email)
        .first()
    }

    // Create new customer if not found
    if (!customer) {
      customer = await ZnUser.create({
        firstName: request.customerInfo.name?.split(' ')[0] || 'Guest',
        lastName: request.customerInfo.name?.split(' ').slice(1).join(' ') || '',
        email: request.customerInfo.email,
        phoneNumber: request.customerInfo.phone,
        isActive: true,
      })
    }

    return customer
  }

  /**
   * Get services by IDs or names
   */
  private async getServicesById(serviceIds: string[]): Promise<ZnStoreService[]> {
    const services = await ZnStoreService.query()
      .whereIn('id', serviceIds)
      .orWhereIn('name', serviceIds)
    
    return services
  }

  /**
   * Get packages by IDs or names
   */
  private async getPackagesById(packageIds: string[]): Promise<ZnStorePackage[]> {
    const packages = await ZnStorePackage.query()
      .whereIn('id', packageIds)
      .orWhereIn('name', packageIds)
    
    return packages
  }

  /**
   * Calculate pricing for services and packages
   */
  private async calculatePricing(
    services: ZnStoreService[],
    packages: ZnStorePackage[],
    storeId: string
  ): Promise<{
    subtotal: number
    taxAmount: number
    total: number
    estimatedDuration: number
  }> {
    let subtotal = 0
    let estimatedDuration = 0

    // Calculate services
    for (const service of services) {
      subtotal += service.price || 0
      estimatedDuration += service.duration || 60
    }

    // Calculate packages
    for (const pkg of packages) {
      subtotal += pkg.price || 0
      // Packages might have their own duration logic
    }

    // Get tax rate for store
    const store = await ZnStore.query()
      .where('id', storeId)
      .preload('taxes')
      .first()
    
    let taxRate = 0
    if (store?.taxes && store.taxes.length > 0) {
      taxRate = store.taxes[0].rate || 0
    }

    const taxAmount = subtotal * (taxRate / 100)
    const total = subtotal + taxAmount

    return {
      subtotal,
      taxAmount,
      total,
      estimatedDuration,
    }
  }

  /**
   * Check if time is within store operating hours
   */
  private async isWithinStoreHours(storeId: string, dateTime: DateTime): Promise<boolean> {
    const storeContext = await this.storeContextService.loadStoreContext(storeId)
    const dayOfWeek = dateTime.weekdayLong?.toLowerCase()
    const storeHours = storeContext.workingHours.find(h => h.day === dayOfWeek)
    
    if (!storeHours || !storeHours.isOpen) {
      return false
    }

    const timeString = dateTime.toFormat('HH:mm')
    return timeString >= storeHours.openTime && timeString <= storeHours.closeTime
  }

  /**
   * Get next available time after the requested time
   */
  private async getNextAvailableTime(
    storeId: string,
    requestedTime: DateTime
  ): Promise<DateTime | null> {
    const alternatives = await this.suggestAlternativeTimes(storeId, requestedTime, 60, 1)
    return alternatives.length > 0 ? alternatives[0] : null
  }

  /**
   * Generate confirmation number
   */
  private generateConfirmationNumber(): string {
    const timestamp = Date.now().toString(36).toUpperCase()
    const random = Math.random().toString(36).substr(2, 4).toUpperCase()
    return `AI${timestamp}${random}`
  }

  /**
   * Generate next steps for customer
   */
  private async generateNextSteps(appointment: ZnAppointment): Promise<string[]> {
    const steps = [
      'Your appointment has been confirmed',
      `Appointment ID: ${appointment.id}`,
      `Date: ${DateTime.fromJSDate(appointment.startTime).toFormat('MMM dd, yyyy')}`,
      `Time: ${DateTime.fromJSDate(appointment.startTime).toFormat('h:mm a')}`,
    ]

    // Add store-specific instructions
    const store = await ZnStore.find(appointment.storeId)
    if (store) {
      steps.push(`Location: ${store.address}`)
      if (store.phoneNumber) {
        steps.push(`Contact: ${store.phoneNumber}`)
      }
    }

    steps.push('Please arrive 10 minutes early')
    steps.push('Bring a valid ID')
    
    return steps
  }

  /**
   * Cancel appointment
   */
  async cancelAppointment(
    appointmentId: string,
    reason?: string
  ): Promise<boolean> {
    try {
      const appointment = await ZnAppointment.find(appointmentId)
      if (!appointment) {
        throw new Error('Appointment not found')
      }

      appointment.status = 'cancelled'
      appointment.notes = `${appointment.notes || ''} | Cancelled: ${reason || 'Customer request'}`
      await appointment.save()

      return true
    } catch (error) {
      console.error('Error cancelling appointment:', error)
      return false
    }
  }

  /**
   * Reschedule appointment
   */
  async rescheduleAppointment(
    appointmentId: string,
    newDateTime: DateTime,
    reason?: string
  ): Promise<BookingConfirmation | null> {
    try {
      const appointment = await ZnAppointment.find(appointmentId)
      if (!appointment) {
        throw new Error('Appointment not found')
      }

      // Check availability for new time
      const duration = DateTime.fromJSDate(appointment.endTime)
        .diff(DateTime.fromJSDate(appointment.startTime), 'minutes').minutes
      
      const availability = await this.checkAvailability(
        appointment.storeId,
        newDateTime,
        duration
      )

      if (!availability.isAvailable) {
        throw new Error(`New time slot not available: ${availability.conflictReason}`)
      }

      // Update appointment
      appointment.startTime = newDateTime.toJSDate()
      appointment.endTime = newDateTime.plus({ minutes: duration }).toJSDate()
      appointment.notes = `${appointment.notes || ''} | Rescheduled: ${reason || 'Customer request'}`
      await appointment.save()

      // Generate new confirmation
      return {
        appointmentId: appointment.id,
        confirmationNumber: this.generateConfirmationNumber(),
        appointment,
        totalPrice: appointment.total,
        estimatedDuration: duration,
        nextSteps: await this.generateNextSteps(appointment),
      }
    } catch (error) {
      console.error('Error rescheduling appointment:', error)
      return null
    }
  }
}