import { inject } from '@adonisjs/core'
import OpenAI from 'openai'
import { DateTime } from 'luxon'
import * as chrono from 'chrono-node'
import Fuse from 'fuse.js'
import StoreContextService, { ServiceInfo } from './store_context_service.js'
import CustomerContextService from './customer_context_service.js'

export interface BookingIntent {
  intent: 'book_appointment' | 'check_availability' | 'cancel_appointment' | 'reschedule_appointment' | 'get_services' | 'get_hours' | 'unknown'
  confidence: number
  entities: BookingEntity[]
  originalText: string
}

export interface BookingEntity {
  type: 'service' | 'date' | 'time' | 'staff' | 'customer_info' | 'duration'
  value: string
  confidence: number
  normalized?: any
  alternatives?: string[]
}

export interface ParsedDateTime {
  date: DateTime | null
  time: DateTime | null
  isRelative: boolean
  confidence: number
  originalText: string
}

export interface ServiceMatch {
  service: ServiceInfo
  confidence: number
  matchType: 'exact' | 'fuzzy' | 'category'
}

@inject()
export default class BookingNLPService {
  private openai: OpenAI
  private fuse: Fuse<ServiceInfo> | null = null
  private servicesList: ServiceInfo[] = []

  constructor(
    private storeContextService: StoreContextService,
    private customerContextService: CustomerContextService
  ) {
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    })
  }

  /**
   * Process booking request and extract intent and entities
   */
  async processBookingRequest(
    text: string,
    storeId: string,
    customerId?: string
  ): Promise<BookingIntent> {
    // Load store context for service matching
    await this.loadStoreServices(storeId)

    // Extract intent using OpenAI
    const intent = await this.extractIntent(text)
    
    // Extract entities
    const entities = await this.extractEntities(text, storeId, customerId)

    return {
      intent: intent.intent,
      confidence: intent.confidence,
      entities,
      originalText: text,
    }
  }

  /**
   * Extract booking intent from text
   */
  private async extractIntent(text: string): Promise<{ intent: BookingIntent['intent'], confidence: number }> {
    const prompt = `
Analyze the following text and determine the booking intent. Respond with JSON only.

Text: "${text}"

Possible intents:
- book_appointment: User wants to book a new appointment
- check_availability: User wants to check available times
- cancel_appointment: User wants to cancel an existing appointment
- reschedule_appointment: User wants to change an existing appointment
- get_services: User wants to know about available services
- get_hours: User wants to know business hours
- unknown: Intent is unclear or not booking-related

Respond with:
{
  "intent": "intent_name",
  "confidence": 0.95
}
`

    try {
      const response = await this.openai.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages: [{ role: 'user', content: prompt }],
        temperature: 0.1,
        max_tokens: 100,
      })

      const result = JSON.parse(response.choices[0].message.content || '{}')
      return {
        intent: result.intent || 'unknown',
        confidence: result.confidence || 0.5,
      }
    } catch (error) {
      console.error('Intent extraction error:', error)
      return { intent: 'unknown', confidence: 0.1 }
    }
  }

  /**
   * Extract entities from text
   */
  private async extractEntities(
    text: string,
    storeId: string,
    customerId?: string
  ): Promise<BookingEntity[]> {
    const entities: BookingEntity[] = []

    // Extract date/time entities
    const dateTimeEntities = await this.extractDateTime(text)
    entities.push(...dateTimeEntities)

    // Extract service entities
    const serviceEntities = await this.extractServices(text, storeId)
    entities.push(...serviceEntities)

    // Extract staff entities (if mentioned)
    const staffEntities = await this.extractStaff(text)
    entities.push(...staffEntities)

    // Extract customer info entities
    const customerEntities = await this.extractCustomerInfo(text)
    entities.push(...customerEntities)

    return entities
  }

  /**
   * Extract date and time entities using chrono-node
   */
  private async extractDateTime(text: string): Promise<BookingEntity[]> {
    const entities: BookingEntity[] = []
    
    try {
      const results = chrono.parse(text)
      
      for (const result of results) {
        const startDate = result.start.date()
        const endDate = result.end?.date()
        
        // Extract date
        if (startDate) {
          entities.push({
            type: 'date',
            value: result.text,
            confidence: 0.9,
            normalized: DateTime.fromJSDate(startDate),
          })
        }
        
        // Extract time if different from date
        if (result.start.get('hour') !== undefined) {
          entities.push({
            type: 'time',
            value: result.text,
            confidence: 0.9,
            normalized: DateTime.fromJSDate(startDate),
          })
        }
        
        // Extract duration if end time is specified
        if (endDate && startDate) {
          const duration = (endDate.getTime() - startDate.getTime()) / (1000 * 60) // minutes
          entities.push({
            type: 'duration',
            value: result.text,
            confidence: 0.8,
            normalized: duration,
          })
        }
      }
    } catch (error) {
      console.error('DateTime extraction error:', error)
    }
    
    return entities
  }

  /**
   * Extract service entities using fuzzy matching
   */
  private async extractServices(text: string, storeId: string): Promise<BookingEntity[]> {
    const entities: BookingEntity[] = []
    
    if (!this.fuse || this.servicesList.length === 0) {
      await this.loadStoreServices(storeId)
    }
    
    if (!this.fuse) return entities
    
    // Search for service matches
    const results = this.fuse.search(text, { limit: 5 })
    
    for (const result of results) {
      if (result.score && result.score < 0.6) { // Good match threshold
        entities.push({
          type: 'service',
          value: result.item.name,
          confidence: 1 - result.score,
          normalized: result.item,
        })
      }
    }
    
    return entities
  }

  /**
   * Extract staff entities
   */
  private async extractStaff(text: string): Promise<BookingEntity[]> {
    const entities: BookingEntity[] = []
    
    // Simple pattern matching for staff names
    const staffPatterns = [
      /with\s+([A-Z][a-z]+)/gi,
      /by\s+([A-Z][a-z]+)/gi,
      /([A-Z][a-z]+)\s+please/gi,
    ]
    
    for (const pattern of staffPatterns) {
      const matches = text.matchAll(pattern)
      for (const match of matches) {
        if (match[1]) {
          entities.push({
            type: 'staff',
            value: match[1],
            confidence: 0.7,
          })
        }
      }
    }
    
    return entities
  }

  /**
   * Extract customer information entities
   */
  private async extractCustomerInfo(text: string): Promise<BookingEntity[]> {
    const entities: BookingEntity[] = []
    
    // Extract phone numbers
    const phonePattern = /\b\d{3}[-.]?\d{3}[-.]?\d{4}\b/g
    const phoneMatches = text.matchAll(phonePattern)
    for (const match of phoneMatches) {
      entities.push({
        type: 'customer_info',
        value: match[0],
        confidence: 0.9,
        normalized: { type: 'phone', value: match[0] },
      })
    }
    
    // Extract email addresses
    const emailPattern = /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g
    const emailMatches = text.matchAll(emailPattern)
    for (const match of emailMatches) {
      entities.push({
        type: 'customer_info',
        value: match[0],
        confidence: 0.95,
        normalized: { type: 'email', value: match[0] },
      })
    }
    
    // Extract names (simple pattern)
    const namePattern = /my name is\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)?)/gi
    const nameMatches = text.matchAll(namePattern)
    for (const match of nameMatches) {
      if (match[1]) {
        entities.push({
          type: 'customer_info',
          value: match[1],
          confidence: 0.8,
          normalized: { type: 'name', value: match[1] },
        })
      }
    }
    
    return entities
  }

  /**
   * Load store services for fuzzy matching
   */
  private async loadStoreServices(storeId: string): Promise<void> {
    try {
      const storeContext = await this.storeContextService.loadStoreContext(storeId)
      this.servicesList = storeContext.services
      
      // Initialize Fuse.js for fuzzy searching
      this.fuse = new Fuse(this.servicesList, {
        keys: ['name', 'category'],
        threshold: 0.6,
        includeScore: true,
      })
    } catch (error) {
      console.error('Error loading store services:', error)
    }
  }

  /**
   * Parse date and time from natural language
   */
  async parseDateTime(text: string, referenceDate?: DateTime): Promise<ParsedDateTime> {
    const reference = referenceDate || DateTime.now()
    
    try {
      const results = chrono.parse(text, reference.toJSDate())
      
      if (results.length === 0) {
        return {
          date: null,
          time: null,
          isRelative: false,
          confidence: 0,
          originalText: text,
        }
      }
      
      const result = results[0]
      const parsedDate = DateTime.fromJSDate(result.start.date())
      
      return {
        date: parsedDate,
        time: parsedDate,
        isRelative: result.start.isCertain('day') === false,
        confidence: 0.9,
        originalText: result.text,
      }
    } catch (error) {
      console.error('DateTime parsing error:', error)
      return {
        date: null,
        time: null,
        isRelative: false,
        confidence: 0,
        originalText: text,
      }
    }
  }

  /**
   * Identify services from text using fuzzy matching
   */
  async identifyServices(text: string, storeId: string): Promise<ServiceMatch[]> {
    await this.loadStoreServices(storeId)
    
    if (!this.fuse) return []
    
    const results = this.fuse.search(text, { limit: 10 })
    
    return results
      .filter(result => result.score !== undefined && result.score < 0.8)
      .map(result => ({
        service: result.item,
        confidence: 1 - (result.score || 1),
        matchType: (result.score || 1) < 0.3 ? 'exact' : 'fuzzy',
      }))
  }

  /**
   * Parse intent response from OpenAI
   */
  parseIntentResponse(response: string): { intent: string; confidence: number; entities: any[] } {
    try {
      const parsed = JSON.parse(response)
      return {
        intent: parsed.intent || 'unknown',
        confidence: parsed.confidence || 0.5,
        entities: parsed.entities || [],
      }
    } catch (error) {
      console.error('Intent response parsing error:', error)
      return {
        intent: 'unknown',
        confidence: 0.1,
        entities: [],
      }
    }
  }

  /**
   * Validate booking entities
   */
  validateBookingEntities(entities: BookingEntity[]): {
    isValid: boolean
    missingEntities: string[]
    conflicts: string[]
  } {
    const missingEntities: string[] = []
    const conflicts: string[] = []
    
    // Check for required entities
    const hasService = entities.some(e => e.type === 'service')
    const hasDateTime = entities.some(e => e.type === 'date' || e.type === 'time')
    
    if (!hasService) {
      missingEntities.push('service')
    }
    
    if (!hasDateTime) {
      missingEntities.push('date_time')
    }
    
    // Check for conflicts
    const dateEntities = entities.filter(e => e.type === 'date')
    if (dateEntities.length > 1) {
      conflicts.push('Multiple dates specified')
    }
    
    const serviceEntities = entities.filter(e => e.type === 'service')
    if (serviceEntities.length > 3) {
      conflicts.push('Too many services specified')
    }
    
    return {
      isValid: missingEntities.length === 0 && conflicts.length === 0,
      missingEntities,
      conflicts,
    }
  }

  /**
   * Generate clarification questions for missing entities
   */
  generateClarificationQuestions(missingEntities: string[]): string[] {
    const questions: string[] = []
    
    if (missingEntities.includes('service')) {
      questions.push('What service would you like to book?')
    }
    
    if (missingEntities.includes('date_time')) {
      questions.push('When would you like to schedule your appointment?')
    }
    
    if (missingEntities.includes('customer_info')) {
      questions.push('Could you please provide your contact information?')
    }
    
    return questions
  }

  /**
   * Extract booking summary from entities
   */
  extractBookingSummary(entities: BookingEntity[]): {
    services: string[]
    dateTime?: DateTime
    duration?: number
    staff?: string
    customerInfo?: any
  } {
    const services = entities
      .filter(e => e.type === 'service')
      .map(e => e.value)
    
    const dateTimeEntity = entities.find(e => e.type === 'date' || e.type === 'time')
    const dateTime = dateTimeEntity?.normalized as DateTime
    
    const durationEntity = entities.find(e => e.type === 'duration')
    const duration = durationEntity?.normalized as number
    
    const staffEntity = entities.find(e => e.type === 'staff')
    const staff = staffEntity?.value
    
    const customerInfoEntities = entities.filter(e => e.type === 'customer_info')
    const customerInfo = customerInfoEntities.reduce((acc, entity) => {
      if (entity.normalized) {
        acc[entity.normalized.type] = entity.normalized.value
      }
      return acc
    }, {} as any)
    
    return {
      services,
      dateTime,
      duration,
      staff,
      customerInfo: Object.keys(customerInfo).length > 0 ? customerInfo : undefined,
    }
  }
}