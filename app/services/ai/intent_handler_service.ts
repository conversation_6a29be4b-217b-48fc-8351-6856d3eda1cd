import { inject } from '@adonisjs/core'
import { DateTime } from 'luxon'
import BookingNLPService, { BookingIntent, BookingEntity } from './booking_nlp_service.js'
import SessionManagementService, { ConversationSession } from './session_management_service.js'
import AppointmentBookingService, { BookingRequest } from './appointment_booking_service.js'
import SchedulingService, { SchedulingRequest } from './scheduling_service.js'
import StoreContextService from './store_context_service.js'
import CustomerContextService from './customer_context_service.js'

export interface IntentHandlerResult {
  response: string
  nextAction?: 'collect_info' | 'confirm_booking' | 'show_availability' | 'end_conversation'
  data?: any
  suggestions?: string[]
  requiresConfirmation?: boolean
  clarificationNeeded?: string[]
}

export interface ConversationContext {
  sessionId: string
  storeId: string
  customerId?: string
  currentIntent?: string
  collectedEntities: Map<string, any>
  conversationState: 'greeting' | 'collecting' | 'confirming' | 'booking' | 'completed' | 'error'
  retryCount: number
  lastActivity: DateTime
}

export interface IntentProcessor {
  canHandle(intent: string): boolean
  process(
    intent: BookingIntent,
    session: ConversationSession,
    context: ConversationContext
  ): Promise<IntentHandlerResult>
}

export interface BookingFlow {
  currentStep: string
  requiredFields: string[]
  collectedFields: string[]
  missingFields: string[]
  validationErrors: string[]
}

@inject()
export default class IntentHandlerService {
  private intentProcessors: Map<string, IntentProcessor> = new Map()
  private readonly MAX_RETRY_COUNT = 3
  private readonly SESSION_TIMEOUT = 30 // minutes

  constructor(
    private bookingNLPService: BookingNLPService,
    private sessionService: SessionManagementService,
    private appointmentService: AppointmentBookingService,
    private schedulingService: SchedulingService,
    private storeContextService: StoreContextService,
    private customerContextService: CustomerContextService
  ) {
    this.initializeIntentProcessors()
  }

  /**
   * Process incoming message and determine appropriate response
   */
  async processMessage(
    message: string,
    sessionId: string,
    storeId: string,
    customerId?: string
  ): Promise<IntentHandlerResult> {
    try {
      // Get or create session
      let session = await this.sessionService.getSession(sessionId)
      if (!session) {
        session = await this.sessionService.createSession(sessionId, storeId, customerId)
      }

      // Add message to session
      await this.sessionService.addMessage(sessionId, {
        role: 'user',
        content: message,
        timestamp: DateTime.now(),
      })

      // Process message with NLP
      const intent = await this.bookingNLPService.processBookingRequest(message, storeId)
      
      // Create conversation context
      const context: ConversationContext = {
        sessionId,
        storeId,
        customerId,
        currentIntent: intent.intent,
        collectedEntities: new Map(Object.entries(session.entities || {})),
        conversationState: session.status as any,
        retryCount: session.retryCount || 0,
        lastActivity: DateTime.now(),
      }

      // Update session with new entities
      await this.updateSessionEntities(sessionId, intent.entities)

      // Process intent
      const result = await this.processIntent(intent, session, context)

      // Add response to session
      await this.sessionService.addMessage(sessionId, {
        role: 'assistant',
        content: result.response,
        timestamp: DateTime.now(),
      })

      // Update session state
      await this.updateSessionState(sessionId, result, context)

      return result
    } catch (error) {
      console.error('Error processing message:', error)
      return this.handleError(error, sessionId)
    }
  }

  /**
   * Process specific intent based on type
   */
  private async processIntent(
    intent: BookingIntent,
    session: ConversationSession,
    context: ConversationContext
  ): Promise<IntentHandlerResult> {
    const processor = this.intentProcessors.get(intent.intent)
    
    if (processor && processor.canHandle(intent.intent)) {
      return await processor.process(intent, session, context)
    }

    // Fallback to default processing
    return await this.processDefaultIntent(intent, session, context)
  }

  /**
   * Initialize intent processors
   */
  private initializeIntentProcessors(): void {
    // Booking intent processor
    this.intentProcessors.set('book_appointment', {
      canHandle: (intent: string) => intent === 'book_appointment',
      process: this.processBookingIntent.bind(this),
    })

    // Check availability processor
    this.intentProcessors.set('check_availability', {
      canHandle: (intent: string) => intent === 'check_availability',
      process: this.processAvailabilityIntent.bind(this),
    })

    // Cancel appointment processor
    this.intentProcessors.set('cancel_appointment', {
      canHandle: (intent: string) => intent === 'cancel_appointment',
      process: this.processCancelIntent.bind(this),
    })

    // Reschedule appointment processor
    this.intentProcessors.set('reschedule_appointment', {
      canHandle: (intent: string) => intent === 'reschedule_appointment',
      process: this.processRescheduleIntent.bind(this),
    })

    // Get information processor
    this.intentProcessors.set('get_info', {
      canHandle: (intent: string) => intent === 'get_info',
      process: this.processInfoIntent.bind(this),
    })

    // Greeting processor
    this.intentProcessors.set('greeting', {
      canHandle: (intent: string) => intent === 'greeting',
      process: this.processGreetingIntent.bind(this),
    })
  }

  /**
   * Process booking appointment intent
   */
  private async processBookingIntent(
    intent: BookingIntent,
    session: ConversationSession,
    context: ConversationContext
  ): Promise<IntentHandlerResult> {
    const bookingFlow = this.analyzeBookingFlow(intent.entities, session)
    
    // Check if we have all required information
    if (bookingFlow.missingFields.length > 0) {
      return await this.requestMissingInformation(bookingFlow, context)
    }

    // Validate collected information
    const validation = await this.validateBookingInformation(intent.entities, context.storeId)
    if (!validation.isValid) {
      return {
        response: `I found some issues with your booking request: ${validation.errors.join(', ')}. Please provide the correct information.`,
        nextAction: 'collect_info',
        clarificationNeeded: validation.errors,
      }
    }

    // Check availability
    const schedulingRequest: SchedulingRequest = {
      storeId: context.storeId,
      serviceIds: intent.entities.services || [],
      preferredDate: intent.entities.date,
      preferredTime: intent.entities.time,
      staffId: intent.entities.staff,
      customerId: context.customerId,
      flexibility: 'moderate',
    }

    const availability = await this.schedulingService.findAvailableSlots(schedulingRequest)
    
    if (availability.availableSlots.length === 0) {
      return await this.handleNoAvailability(schedulingRequest, context)
    }

    // Present booking options
    return await this.presentBookingOptions(availability, intent.entities, context)
  }

  /**
   * Process availability check intent
   */
  private async processAvailabilityIntent(
    intent: BookingIntent,
    session: ConversationSession,
    context: ConversationContext
  ): Promise<IntentHandlerResult> {
    const { date, time, services, staff } = intent.entities
    
    if (!date) {
      return {
        response: "I'd be happy to check availability for you! What date are you looking for?",
        nextAction: 'collect_info',
        clarificationNeeded: ['date'],
      }
    }

    const schedulingRequest: SchedulingRequest = {
      storeId: context.storeId,
      serviceIds: services || [],
      preferredDate: date,
      preferredTime: time,
      staffId: staff,
      flexibility: 'flexible',
    }

    const availability = await this.schedulingService.findAvailableSlots(schedulingRequest)
    
    if (availability.availableSlots.length === 0) {
      return {
        response: `I don't see any availability for ${date.toFormat('MMMM d')}. Would you like me to suggest some alternative dates?`,
        nextAction: 'show_availability',
        data: { alternatives: availability.alternatives },
        suggestions: ['Show alternatives', 'Try different date'],
      }
    }

    const slots = availability.availableSlots.slice(0, 5)
    const slotDescriptions = slots.map(slot => 
      `${slot.startTime.toFormat('h:mm a')} - ${slot.endTime.toFormat('h:mm a')}`
    ).join(', ')

    return {
      response: `Great! I found several available times on ${date.toFormat('MMMM d')}: ${slotDescriptions}. Would you like to book one of these slots?`,
      nextAction: 'confirm_booking',
      data: { availableSlots: slots },
      suggestions: ['Book first slot', 'See more options'],
    }
  }

  /**
   * Process cancel appointment intent
   */
  private async processCancelIntent(
    intent: BookingIntent,
    session: ConversationSession,
    context: ConversationContext
  ): Promise<IntentHandlerResult> {
    const { appointmentId, date, time } = intent.entities
    
    if (!appointmentId && !date) {
      return {
        response: "I can help you cancel an appointment. Could you provide the appointment ID or the date and time of your appointment?",
        nextAction: 'collect_info',
        clarificationNeeded: ['appointmentId', 'date'],
      }
    }

    try {
      // Find appointment to cancel
      let appointment
      if (appointmentId) {
        appointment = await this.appointmentService.findAppointment(appointmentId)
      } else if (date && context.customerId) {
        // Find appointment by date and customer
        appointment = await this.appointmentService.findAppointmentByDateAndCustomer(
          context.customerId,
          date,
          time
        )
      }

      if (!appointment) {
        return {
          response: "I couldn't find an appointment with those details. Could you double-check the information?",
          nextAction: 'collect_info',
        }
      }

      // Cancel the appointment
      await this.appointmentService.cancelAppointment(appointment.id, 'Customer requested cancellation')

      return {
        response: `Your appointment on ${appointment.date.toFormat('MMMM d')} at ${appointment.time.toFormat('h:mm a')} has been successfully cancelled. Is there anything else I can help you with?`,
        nextAction: 'end_conversation',
        data: { cancelledAppointment: appointment },
      }
    } catch (error) {
      return {
        response: "I'm sorry, I encountered an issue while trying to cancel your appointment. Please try again or contact us directly.",
        nextAction: 'end_conversation',
      }
    }
  }

  /**
   * Process reschedule appointment intent
   */
  private async processRescheduleIntent(
    intent: BookingIntent,
    session: ConversationSession,
    context: ConversationContext
  ): Promise<IntentHandlerResult> {
    const { appointmentId, date, time, newDate, newTime } = intent.entities
    
    if (!appointmentId && !date) {
      return {
        response: "I can help you reschedule an appointment. Could you provide the appointment ID or the current date and time?",
        nextAction: 'collect_info',
        clarificationNeeded: ['appointmentId', 'date'],
      }
    }

    if (!newDate && !newTime) {
      return {
        response: "What date and time would you like to reschedule to?",
        nextAction: 'collect_info',
        clarificationNeeded: ['newDate', 'newTime'],
      }
    }

    try {
      // Find existing appointment
      let appointment
      if (appointmentId) {
        appointment = await this.appointmentService.findAppointment(appointmentId)
      } else if (date && context.customerId) {
        appointment = await this.appointmentService.findAppointmentByDateAndCustomer(
          context.customerId,
          date,
          time
        )
      }

      if (!appointment) {
        return {
          response: "I couldn't find an appointment with those details. Could you provide the correct information?",
          nextAction: 'collect_info',
        }
      }

      // Check availability for new time
      const newDateTime = newDate || appointment.date
      const newTimeSlot = newTime || appointment.time
      
      const availability = await this.schedulingService.checkSlotAvailability(
        context.storeId,
        newDateTime.set({ hour: newTimeSlot.hour, minute: newTimeSlot.minute }),
        appointment.duration
      )

      if (!availability.available) {
        return {
          response: `Unfortunately, ${newDateTime.toFormat('MMMM d')} at ${newTimeSlot.toFormat('h:mm a')} is not available. Here are some alternative times: ${availability.suggestions.map(s => s.toFormat('h:mm a')).join(', ')}.`,
          nextAction: 'show_availability',
          data: { suggestions: availability.suggestions },
        }
      }

      // Reschedule the appointment
      const rescheduledAppointment = await this.appointmentService.rescheduleAppointment(
        appointment.id,
        newDateTime,
        newTimeSlot
      )

      return {
        response: `Perfect! Your appointment has been rescheduled to ${newDateTime.toFormat('MMMM d')} at ${newTimeSlot.toFormat('h:mm a')}. You'll receive a confirmation shortly.`,
        nextAction: 'end_conversation',
        data: { rescheduledAppointment },
      }
    } catch (error) {
      return {
        response: "I'm sorry, I encountered an issue while rescheduling your appointment. Please try again or contact us directly.",
        nextAction: 'end_conversation',
      }
    }
  }

  /**
   * Process information request intent
   */
  private async processInfoIntent(
    intent: BookingIntent,
    session: ConversationSession,
    context: ConversationContext
  ): Promise<IntentHandlerResult> {
    const { infoType } = intent.entities
    const storeContext = await this.storeContextService.loadStoreContext(context.storeId)
    
    switch (infoType) {
      case 'hours':
        const hours = storeContext.workingHours
          .filter(h => h.isOpen)
          .map(h => `${h.day}: ${h.openTime} - ${h.closeTime}`)
          .join('\n')
        
        return {
          response: `Here are our operating hours:\n${hours}`,
          nextAction: 'end_conversation',
        }
      
      case 'services':
        const services = storeContext.services
          .map(s => `${s.name} - $${s.price} (${s.duration} min)`)
          .join('\n')
        
        return {
          response: `Here are our available services:\n${services}\n\nWould you like to book any of these services?`,
          nextAction: 'collect_info',
          suggestions: ['Book appointment', 'Check availability'],
        }
      
      case 'location':
        return {
          response: `We're located at ${storeContext.address}. You can reach us at ${storeContext.phoneNumber}.`,
          nextAction: 'end_conversation',
        }
      
      case 'pricing':
        const pricing = storeContext.services
          .map(s => `${s.name}: $${s.price}`)
          .join('\n')
        
        return {
          response: `Here's our pricing:\n${pricing}`,
          nextAction: 'end_conversation',
        }
      
      default:
        return {
          response: "I can provide information about our hours, services, location, or pricing. What would you like to know?",
          nextAction: 'collect_info',
          suggestions: ['Hours', 'Services', 'Location', 'Pricing'],
        }
    }
  }

  /**
   * Process greeting intent
   */
  private async processGreetingIntent(
    intent: BookingIntent,
    session: ConversationSession,
    context: ConversationContext
  ): Promise<IntentHandlerResult> {
    const storeContext = await this.storeContextService.loadStoreContext(context.storeId)
    const currentStatus = storeContext.isCurrentlyOpen ? 'open' : 'closed'
    
    let greeting = `Hello! Welcome to ${storeContext.name}. We're currently ${currentStatus}.`
    
    if (context.customerId) {
      const customerContext = await this.customerContextService.loadCustomerContext(
        context.customerId,
        context.storeId
      )
      
      if (customerContext.isVIP) {
        greeting += ` Great to see you again, ${customerContext.name}!`
      }
    }
    
    greeting += " How can I help you today?"
    
    return {
      response: greeting,
      nextAction: 'collect_info',
      suggestions: ['Book appointment', 'Check availability', 'View services', 'Store hours'],
    }
  }

  /**
   * Process default/unknown intent
   */
  private async processDefaultIntent(
    intent: BookingIntent,
    session: ConversationSession,
    context: ConversationContext
  ): Promise<IntentHandlerResult> {
    // Increment retry count
    await this.sessionService.incrementRetryCount(context.sessionId)
    
    if (context.retryCount >= this.MAX_RETRY_COUNT) {
      return {
        response: "I'm having trouble understanding your request. Would you like me to connect you with a human representative?",
        nextAction: 'end_conversation',
        suggestions: ['Connect to human', 'Start over'],
      }
    }
    
    return {
      response: "I'm not sure I understand. Could you please rephrase your request? I can help you book appointments, check availability, or provide information about our services.",
      nextAction: 'collect_info',
      suggestions: ['Book appointment', 'Check availability', 'Get information'],
    }
  }

  /**
   * Helper methods
   */
  private analyzeBookingFlow(entities: BookingEntity, session: ConversationSession): BookingFlow {
    const requiredFields = ['services', 'date', 'time']
    const collectedFields: string[] = []
    const missingFields: string[] = []
    
    // Check what we have from current entities and session
    const allEntities = { ...session.entities, ...entities }
    
    requiredFields.forEach(field => {
      if (allEntities[field]) {
        collectedFields.push(field)
      } else {
        missingFields.push(field)
      }
    })
    
    return {
      currentStep: missingFields.length > 0 ? 'collecting' : 'confirming',
      requiredFields,
      collectedFields,
      missingFields,
      validationErrors: [],
    }
  }

  private async requestMissingInformation(
    bookingFlow: BookingFlow,
    context: ConversationContext
  ): Promise<IntentHandlerResult> {
    const missingField = bookingFlow.missingFields[0]
    let response = ''
    let suggestions: string[] = []
    
    switch (missingField) {
      case 'services':
        const storeContext = await this.storeContextService.loadStoreContext(context.storeId)
        const serviceNames = storeContext.services.map(s => s.name)
        response = `What service would you like to book? We offer: ${serviceNames.join(', ')}.`
        suggestions = serviceNames.slice(0, 3)
        break
      
      case 'date':
        response = "What date would you prefer for your appointment?"
        suggestions = ['Today', 'Tomorrow', 'This weekend']
        break
      
      case 'time':
        response = "What time works best for you?"
        suggestions = ['Morning', 'Afternoon', 'Evening']
        break
      
      default:
        response = `I need some more information to book your appointment. Could you provide ${missingField}?`
    }
    
    return {
      response,
      nextAction: 'collect_info',
      clarificationNeeded: [missingField],
      suggestions,
    }
  }

  private async validateBookingInformation(
    entities: BookingEntity,
    storeId: string
  ): Promise<{ isValid: boolean; errors: string[] }> {
    const errors: string[] = []
    
    // Validate services exist
    if (entities.services && entities.services.length > 0) {
      const storeContext = await this.storeContextService.loadStoreContext(storeId)
      const validServiceIds = storeContext.services.map(s => s.id)
      
      const invalidServices = entities.services.filter(id => !validServiceIds.includes(id))
      if (invalidServices.length > 0) {
        errors.push('Some selected services are not available')
      }
    }
    
    // Validate date is not in the past
    if (entities.date && entities.date < DateTime.now().startOf('day')) {
      errors.push('Cannot book appointments in the past')
    }
    
    // Validate time is within store hours
    if (entities.date && entities.time) {
      const storeContext = await this.storeContextService.loadStoreContext(storeId)
      const dayOfWeek = entities.date.weekdayLong?.toLowerCase()
      const storeHours = storeContext.workingHours.find(h => h.day === dayOfWeek)
      
      if (!storeHours || !storeHours.isOpen) {
        errors.push('Store is closed on the selected date')
      } else {
        const timeString = entities.time.toFormat('HH:mm')
        if (timeString < storeHours.openTime || timeString > storeHours.closeTime) {
          errors.push('Selected time is outside store hours')
        }
      }
    }
    
    return {
      isValid: errors.length === 0,
      errors,
    }
  }

  private async handleNoAvailability(
    request: SchedulingRequest,
    context: ConversationContext
  ): Promise<IntentHandlerResult> {
    // Try to find alternatives
    const alternativeRequest = {
      ...request,
      flexibility: 'flexible' as const,
    }
    
    const alternatives = await this.schedulingService.findAvailableSlots(alternativeRequest)
    
    if (alternatives.availableSlots.length > 0) {
      const altSlots = alternatives.availableSlots.slice(0, 3)
      const altDescriptions = altSlots.map(slot => 
        `${slot.startTime.toFormat('MMMM d')} at ${slot.startTime.toFormat('h:mm a')}`
      ).join(', ')
      
      return {
        response: `I don't have availability at your preferred time, but I found these alternatives: ${altDescriptions}. Would any of these work for you?`,
        nextAction: 'show_availability',
        data: { alternatives: altSlots },
        suggestions: ['Book alternative', 'Try different date'],
      }
    }
    
    return {
      response: "I'm sorry, but I don't have any availability for your requested time. Would you like me to check a different date or put you on our waitlist?",
      nextAction: 'collect_info',
      suggestions: ['Different date', 'Join waitlist', 'Call directly'],
    }
  }

  private async presentBookingOptions(
    availability: any,
    entities: BookingEntity,
    context: ConversationContext
  ): Promise<IntentHandlerResult> {
    const recommendedSlot = availability.recommendedSlot
    
    if (recommendedSlot) {
      const services = await this.getServiceNames(entities.services || [], context.storeId)
      
      return {
        response: `Perfect! I found availability for ${services.join(', ')} on ${recommendedSlot.startTime.toFormat('MMMM d')} at ${recommendedSlot.startTime.toFormat('h:mm a')}. The total will be $${recommendedSlot.totalPrice}. Shall I book this for you?`,
        nextAction: 'confirm_booking',
        data: { recommendedSlot, entities },
        requiresConfirmation: true,
        suggestions: ['Yes, book it', 'See other times', 'Modify booking'],
      }
    }
    
    return {
      response: "I found some availability, but let me get a few more details to find the perfect time for you.",
      nextAction: 'collect_info',
    }
  }

  private async updateSessionEntities(sessionId: string, entities: BookingEntity): Promise<void> {
    const entityUpdates: Record<string, any> = {}
    
    Object.entries(entities).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        entityUpdates[key] = value
      }
    })
    
    if (Object.keys(entityUpdates).length > 0) {
      await this.sessionService.addEntities(sessionId, entityUpdates)
    }
  }

  private async updateSessionState(
    sessionId: string,
    result: IntentHandlerResult,
    context: ConversationContext
  ): Promise<void> {
    let newStatus = context.conversationState
    
    switch (result.nextAction) {
      case 'collect_info':
        newStatus = 'collecting'
        break
      case 'confirm_booking':
        newStatus = 'confirming'
        break
      case 'end_conversation':
        newStatus = 'completed'
        break
    }
    
    await this.sessionService.updateConversationState(sessionId, newStatus)
    
    if (result.nextAction === 'end_conversation') {
      await this.sessionService.expireSession(sessionId)
    }
  }

  private handleError(error: any, sessionId: string): IntentHandlerResult {
    console.error('Intent handler error:', error)
    
    return {
      response: "I'm sorry, I encountered an unexpected error. Please try again or contact us directly for assistance.",
      nextAction: 'end_conversation',
      suggestions: ['Try again', 'Contact support'],
    }
  }

  private async getServiceNames(serviceIds: string[], storeId: string): Promise<string[]> {
    const storeContext = await this.storeContextService.loadStoreContext(storeId)
    return serviceIds
      .map(id => storeContext.services.find(s => s.id === id)?.name)
      .filter(name => name !== undefined) as string[]
  }
}