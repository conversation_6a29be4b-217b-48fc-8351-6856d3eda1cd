import { inject } from '@adonisjs/core'
import { DateTime } from 'luxon'
import StoreContextService from './store_context_service.js'
import CustomerContextService from './customer_context_service.js'
import BookingNLPService, { BookingIntent } from './booking_nlp_service.js'
import SessionManagementService, { ConversationSession } from './session_management_service.js'
import AppointmentBookingService from './appointment_booking_service.js'
import SchedulingService from './scheduling_service.js'
import IntentHandlerService from './intent_handler_service.js'
import FallbackDialogService from './fallback_dialog_service.js'

export interface AIReceptionistResponse {
  message: string
  sessionId: string
  conversationState: 'greeting' | 'collecting' | 'confirming' | 'completed' | 'error'
  nextAction?: 'continue' | 'confirm' | 'clarify' | 'escalate' | 'complete'
  suggestions?: string[]
  quickActions?: Array<{
    label: string
    action: string
    data?: any
  }>
  bookingProgress?: {
    step: string
    completedSteps: string[]
    nextSteps: string[]
    collectedInfo: Record<string, any>
  }
  metadata?: {
    confidence: number
    processingTime: number
    intent: string
    entities: Record<string, any>
  }
}

export interface ConversationRequest {
  message: string
  sessionId?: string
  storeId: string
  customerId?: string
  customerPhone?: string
  customerName?: string
  metadata?: Record<string, any>
}

export interface AICapabilities {
  naturalLanguageProcessing: boolean
  appointmentBooking: boolean
  availabilityChecking: boolean
  customerRecognition: boolean
  multiLanguageSupport: boolean
  contextAwareness: boolean
  errorRecovery: boolean
  humanHandoff: boolean
}

export interface ConversationAnalytics {
  sessionId: string
  storeId: string
  startTime: DateTime
  endTime?: DateTime
  messageCount: number
  intentChanges: number
  successfulBookings: number
  escalations: number
  averageResponseTime: number
  customerSatisfaction?: number
  completionRate: number
}

@inject()
export default class ZurnoAIReceptionistService {
  private readonly MAX_SESSION_DURATION = 30 * 60 * 1000 // 30 minutes
  private readonly CONFIDENCE_THRESHOLD = 0.7
  private readonly MAX_CLARIFICATION_ATTEMPTS = 3

  constructor(
    private storeContextService: StoreContextService,
    private customerContextService: CustomerContextService,
    private bookingNLPService: BookingNLPService,
    private sessionService: SessionManagementService,
    private appointmentService: AppointmentBookingService,
    private schedulingService: SchedulingService,
    private intentHandlerService: IntentHandlerService,
    private fallbackService: FallbackDialogService
  ) {}

  /**
   * Main entry point for processing user messages
   */
  async processMessage(request: ConversationRequest): Promise<AIReceptionistResponse> {
    const startTime = DateTime.now()
    
    try {
      // Get or create session
      const session = await this.getOrCreateSession(request)
      
      // Process the message through NLP
      const nlpResult = await this.bookingNLPService.processBookingRequest(
        request.message,
        request.storeId,
        session.entities
      )
      
      // Update session with new message and entities
      await this.updateSessionWithMessage(session.id, request.message, nlpResult)
      
      // Handle the intent
      const response = await this.handleIntent(nlpResult, session, request)
      
      // Calculate processing time
      const processingTime = DateTime.now().diff(startTime).milliseconds
      
      // Add metadata
      response.metadata = {
        confidence: nlpResult.confidence,
        processingTime,
        intent: nlpResult.intent,
        entities: nlpResult.entities
      }
      
      return response
      
    } catch (error) {
      console.error('AI Receptionist error:', error)
      return await this.handleError(request, error)
    }
  }

  /**
   * Get conversation history for a session
   */
  async getConversationHistory(sessionId: string): Promise<{
    messages: Array<{
      role: 'user' | 'assistant'
      content: string
      timestamp: DateTime
    }>
    session: ConversationSession | null
  }> {
    const session = await this.sessionService.getSession(sessionId)
    
    if (!session) {
      return { messages: [], session: null }
    }
    
    const messages = session.messages?.map(msg => ({
      role: msg.role as 'user' | 'assistant',
      content: msg.content,
      timestamp: DateTime.fromISO(msg.timestamp)
    })) || []
    
    return { messages, session }
  }

  /**
   * Get AI capabilities for a store
   */
  async getCapabilities(storeId: string): Promise<AICapabilities> {
    // This could be configured per store
    return {
      naturalLanguageProcessing: true,
      appointmentBooking: true,
      availabilityChecking: true,
      customerRecognition: true,
      multiLanguageSupport: false, // Could be enabled based on store settings
      contextAwareness: true,
      errorRecovery: true,
      humanHandoff: true
    }
  }

  /**
   * Get conversation analytics
   */
  async getConversationAnalytics(
    storeId: string,
    startDate: DateTime,
    endDate: DateTime
  ): Promise<ConversationAnalytics[]> {
    // This would integrate with analytics service
    const sessions = await this.sessionService.getSessionsForStore(
      storeId,
      startDate.toJSDate(),
      endDate.toJSDate()
    )
    
    return sessions.map(session => ({
      sessionId: session.id,
      storeId: session.storeId,
      startTime: DateTime.fromJSDate(session.createdAt),
      endTime: session.expiresAt ? DateTime.fromJSDate(session.expiresAt) : undefined,
      messageCount: session.messages?.length || 0,
      intentChanges: this.calculateIntentChanges(session),
      successfulBookings: session.status === 'completed' ? 1 : 0,
      escalations: session.metadata?.escalated ? 1 : 0,
      averageResponseTime: this.calculateAverageResponseTime(session),
      completionRate: session.status === 'completed' ? 1 : 0
    }))
  }

  /**
   * Handle quick actions from UI
   */
  async handleQuickAction(
    sessionId: string,
    action: string,
    data?: any
  ): Promise<AIReceptionistResponse> {
    const session = await this.sessionService.getSession(sessionId)
    if (!session) {
      throw new Error('Session not found')
    }
    
    switch (action) {
      case 'start_booking':
        return await this.startBookingFlow(session)
      
      case 'show_services':
        return await this.showServices(session)
      
      case 'show_hours':
        return await this.showStoreHours(session)
      
      case 'select_service':
        return await this.selectService(session, data)
      
      case 'select_date':
        return await this.selectDate(session, data)
      
      case 'select_time':
        return await this.selectTime(session, data)
      
      case 'confirm_booking':
        return await this.confirmBooking(session)
      
      case 'modify_booking':
        return await this.modifyBooking(session)
      
      case 'cancel_booking':
        return await this.cancelBooking(session)
      
      case 'human_handoff':
        return await this.initiateHumanHandoff(session)
      
      case 'reset_conversation':
        return await this.resetConversation(session)
      
      default:
        return await this.handleUnknownAction(session, action)
    }
  }

  /**
   * Reset conversation to start fresh
   */
  async resetConversation(session: ConversationSession): Promise<AIReceptionistResponse> {
    const fallbackResponse = await this.fallbackService.resetConversation(
      session.id,
      session.storeId
    )
    
    return {
      message: fallbackResponse.message,
      sessionId: session.id,
      conversationState: 'greeting',
      nextAction: 'continue',
      suggestions: fallbackResponse.suggestions,
      quickActions: fallbackResponse.quickActions
    }
  }

  /**
   * Initiate human handoff
   */
  async initiateHumanHandoff(session: ConversationSession): Promise<AIReceptionistResponse> {
    await this.sessionService.updateConversationState(session.id, 'error')
    await this.sessionService.addMetadata(session.id, {
      escalated: true,
      escalationTime: DateTime.now().toISO(),
      escalationReason: 'user_requested'
    })
    
    const storeContext = await this.storeContextService.loadStoreContext(session.storeId)
    
    return {
      message: `I'm connecting you with our team. You can also call us directly at ${storeContext.phoneNumber} for immediate assistance.`,
      sessionId: session.id,
      conversationState: 'error',
      nextAction: 'escalate',
      quickActions: [
        {
          label: 'Call Now',
          action: 'call_store',
          data: { phone: storeContext.phoneNumber }
        }
      ]
    }
  }

  /**
   * Private helper methods
   */
  private async getOrCreateSession(request: ConversationRequest): Promise<ConversationSession> {
    if (request.sessionId) {
      const existingSession = await this.sessionService.getSession(request.sessionId)
      if (existingSession && !this.isSessionExpired(existingSession)) {
        return existingSession
      }
    }
    
    // Create new session
    const sessionId = await this.sessionService.createSession(
      request.storeId,
      request.customerId,
      {
        customerPhone: request.customerPhone,
        customerName: request.customerName,
        ...request.metadata
      }
    )
    
    const session = await this.sessionService.getSession(sessionId)
    if (!session) {
      throw new Error('Failed to create session')
    }
    
    return session
  }

  private async updateSessionWithMessage(
    sessionId: string,
    message: string,
    nlpResult: BookingIntent
  ): Promise<void> {
    // Add user message
    await this.sessionService.addMessage(sessionId, {
      role: 'user',
      content: message,
      timestamp: DateTime.now().toISO()
    })
    
    // Add extracted entities
    if (nlpResult.entities && Object.keys(nlpResult.entities).length > 0) {
      await this.sessionService.addEntities(sessionId, nlpResult.entities)
    }
    
    // Update intent if it changed
    const session = await this.sessionService.getSession(sessionId)
    if (session && session.metadata?.lastIntent !== nlpResult.intent) {
      await this.sessionService.addMetadata(sessionId, {
        lastIntent: nlpResult.intent,
        intentChangeTime: DateTime.now().toISO()
      })
    }
  }

  private async handleIntent(
    nlpResult: BookingIntent,
    session: ConversationSession,
    request: ConversationRequest
  ): Promise<AIReceptionistResponse> {
    // Check confidence level
    if (nlpResult.confidence < this.CONFIDENCE_THRESHOLD) {
      return await this.handleLowConfidence(nlpResult, session, request)
    }
    
    // Use intent handler service
    const handlerResult = await this.intentHandlerService.processMessage(
      request.message,
      session.id,
      request.storeId,
      nlpResult
    )
    
    // Convert handler result to AI receptionist response
    return {
      message: handlerResult.response,
      sessionId: session.id,
      conversationState: this.mapConversationState(handlerResult.nextState),
      nextAction: handlerResult.nextAction,
      suggestions: handlerResult.suggestions,
      quickActions: handlerResult.quickActions,
      bookingProgress: handlerResult.bookingProgress
    }
  }

  private async handleLowConfidence(
    nlpResult: BookingIntent,
    session: ConversationSession,
    request: ConversationRequest
  ): Promise<AIReceptionistResponse> {
    // Increment clarification attempts
    const clarificationCount = (session.metadata?.clarificationAttempts || 0) + 1
    await this.sessionService.addMetadata(session.id, {
      clarificationAttempts: clarificationCount
    })
    
    // If too many clarification attempts, use fallback
    if (clarificationCount > this.MAX_CLARIFICATION_ATTEMPTS) {
      const fallbackResponse = await this.fallbackService.handleFallback(
        request.message,
        session.id,
        request.storeId
      )
      
      return {
        message: fallbackResponse.message,
        sessionId: session.id,
        conversationState: 'error',
        nextAction: fallbackResponse.nextAction,
        suggestions: fallbackResponse.suggestions,
        quickActions: fallbackResponse.quickActions
      }
    }
    
    // Provide clarification help
    const contextualHelp = await this.fallbackService.provideContextualHelp(
      session.id,
      request.storeId
    )
    
    return {
      message: `${contextualHelp.helpMessage} ${contextualHelp.examples.slice(0, 2).join(' or ')}.`,
      sessionId: session.id,
      conversationState: session.status as any,
      nextAction: 'clarify',
      suggestions: contextualHelp.availableActions,
      quickActions: contextualHelp.shortcuts
    }
  }

  private async handleError(
    request: ConversationRequest,
    error: any
  ): Promise<AIReceptionistResponse> {
    console.error('Processing error:', error)
    
    // Try to get session for fallback
    let sessionId = request.sessionId
    if (!sessionId) {
      try {
        sessionId = await this.sessionService.createSession(
          request.storeId,
          request.customerId
        )
      } catch (sessionError) {
        console.error('Failed to create emergency session:', sessionError)
        sessionId = 'emergency-' + Date.now()
      }
    }
    
    // Use fallback service
    const fallbackResponse = await this.fallbackService.handleFallback(
      request.message,
      sessionId,
      request.storeId,
      error
    )
    
    return {
      message: fallbackResponse.message,
      sessionId,
      conversationState: 'error',
      nextAction: fallbackResponse.nextAction,
      suggestions: fallbackResponse.suggestions,
      quickActions: fallbackResponse.quickActions
    }
  }

  private async startBookingFlow(session: ConversationSession): Promise<AIReceptionistResponse> {
    await this.sessionService.updateConversationState(session.id, 'collecting')
    
    const storeContext = await this.storeContextService.loadStoreContext(session.storeId)
    const availableServices = storeContext.services.slice(0, 5) // Show top 5 services
    
    const quickActions = availableServices.map(service => ({
      label: service.name,
      action: 'select_service',
      data: { serviceId: service.id, serviceName: service.name }
    }))
    
    return {
      message: "Great! I'd be happy to help you book an appointment. What service are you interested in?",
      sessionId: session.id,
      conversationState: 'collecting',
      nextAction: 'continue',
      suggestions: availableServices.map(s => s.name),
      quickActions,
      bookingProgress: {
        step: 'service_selection',
        completedSteps: [],
        nextSteps: ['service', 'date', 'time', 'confirmation'],
        collectedInfo: session.entities || {}
      }
    }
  }

  private async showServices(session: ConversationSession): Promise<AIReceptionistResponse> {
    const storeContext = await this.storeContextService.loadStoreContext(session.storeId)
    
    const serviceList = storeContext.services
      .slice(0, 8)
      .map(service => `• ${service.name} (${service.duration} min) - $${service.price}`)
      .join('\n')
    
    const quickActions = storeContext.services.slice(0, 4).map(service => ({
      label: `Book ${service.name}`,
      action: 'select_service',
      data: { serviceId: service.id, serviceName: service.name }
    }))
    
    return {
      message: `Here are our available services:\n\n${serviceList}\n\nWhich service interests you?`,
      sessionId: session.id,
      conversationState: session.status as any,
      nextAction: 'continue',
      suggestions: storeContext.services.slice(0, 4).map(s => s.name),
      quickActions
    }
  }

  private async showStoreHours(session: ConversationSession): Promise<AIReceptionistResponse> {
    const storeContext = await this.storeContextService.loadStoreContext(session.storeId)
    const currentStatus = storeContext.currentStatus
    
    const hoursText = storeContext.workingHours
      .map(wh => `${wh.dayOfWeek}: ${wh.openTime} - ${wh.closeTime}`)
      .join('\n')
    
    const statusMessage = currentStatus.isOpen 
      ? `We're currently open until ${currentStatus.nextCloseTime}.`
      : `We're currently closed. We'll be open again ${currentStatus.nextOpenTime}.`
    
    return {
      message: `${statusMessage}\n\nOur hours are:\n${hoursText}\n\nWould you like to book an appointment?`,
      sessionId: session.id,
      conversationState: session.status as any,
      nextAction: 'continue',
      suggestions: ['Book appointment', 'Check availability', 'More information'],
      quickActions: [
        { label: 'Book Appointment', action: 'start_booking' },
        { label: 'Check Availability', action: 'check_availability' }
      ]
    }
  }

  private async selectService(
    session: ConversationSession,
    data: { serviceId: string; serviceName: string }
  ): Promise<AIReceptionistResponse> {
    // Add service to session entities
    await this.sessionService.addEntities(session.id, {
      serviceId: data.serviceId,
      serviceName: data.serviceName
    })
    
    await this.sessionService.updateConversationState(session.id, 'collecting')
    
    return {
      message: `Perfect! I'll book you for ${data.serviceName}. What date works best for you?`,
      sessionId: session.id,
      conversationState: 'collecting',
      nextAction: 'continue',
      suggestions: ['Today', 'Tomorrow', 'This weekend', 'Next week'],
      quickActions: [
        { label: 'Today', action: 'select_date', data: { date: 'today' } },
        { label: 'Tomorrow', action: 'select_date', data: { date: 'tomorrow' } },
        { label: 'This Weekend', action: 'select_date', data: { date: 'weekend' } }
      ],
      bookingProgress: {
        step: 'date_selection',
        completedSteps: ['service'],
        nextSteps: ['date', 'time', 'confirmation'],
        collectedInfo: { serviceName: data.serviceName, serviceId: data.serviceId }
      }
    }
  }

  private async selectDate(
    session: ConversationSession,
    data: { date: string }
  ): Promise<AIReceptionistResponse> {
    // Process date selection and add to entities
    let dateValue: string
    const today = DateTime.now()
    
    switch (data.date) {
      case 'today':
        dateValue = today.toISODate()!
        break
      case 'tomorrow':
        dateValue = today.plus({ days: 1 }).toISODate()!
        break
      case 'weekend':
        // Find next Saturday
        const nextSaturday = today.plus({ days: (6 - today.weekday + 7) % 7 || 7 })
        dateValue = nextSaturday.toISODate()!
        break
      default:
        dateValue = data.date
    }
    
    await this.sessionService.addEntities(session.id, {
      date: dateValue,
      dateDisplay: data.date
    })
    
    // Check availability for that date
    const entities = (await this.sessionService.getSession(session.id))?.entities
    if (entities?.serviceId) {
      const availability = await this.schedulingService.findAvailableSlots({
        storeId: session.storeId,
        serviceId: entities.serviceId,
        date: DateTime.fromISO(dateValue),
        duration: 60 // Default duration
      })
      
      if (availability.slots.length === 0) {
        return {
          message: `Unfortunately, we don't have availability on ${data.date}. Here are some alternative dates:`,
          sessionId: session.id,
          conversationState: 'collecting',
          nextAction: 'clarify',
          suggestions: availability.alternativeDates?.slice(0, 3) || ['Next week', 'Different day'],
          quickActions: availability.alternativeDates?.slice(0, 3).map(altDate => ({
            label: altDate,
            action: 'select_date',
            data: { date: altDate }
          })) || []
        }
      }
      
      const timeSlots = availability.slots.slice(0, 4).map(slot => 
        DateTime.fromJSDate(slot.startTime).toFormat('h:mm a')
      )
      
      return {
        message: `Great! We have availability on ${data.date}. What time works for you?`,
        sessionId: session.id,
        conversationState: 'collecting',
        nextAction: 'continue',
        suggestions: timeSlots,
        quickActions: timeSlots.map(time => ({
          label: time,
          action: 'select_time',
          data: { time }
        })),
        bookingProgress: {
          step: 'time_selection',
          completedSteps: ['service', 'date'],
          nextSteps: ['time', 'confirmation'],
          collectedInfo: { ...entities, date: dateValue, dateDisplay: data.date }
        }
      }
    }
    
    return {
      message: `Perfect! ${data.date} it is. What time would you prefer?`,
      sessionId: session.id,
      conversationState: 'collecting',
      nextAction: 'continue',
      suggestions: ['Morning', 'Afternoon', 'Evening'],
      quickActions: [
        { label: 'Morning', action: 'select_time', data: { time: 'morning' } },
        { label: 'Afternoon', action: 'select_time', data: { time: 'afternoon' } },
        { label: 'Evening', action: 'select_time', data: { time: 'evening' } }
      ]
    }
  }

  private async selectTime(
    session: ConversationSession,
    data: { time: string }
  ): Promise<AIReceptionistResponse> {
    await this.sessionService.addEntities(session.id, {
      time: data.time
    })
    
    // Check if we have enough info to proceed to confirmation
    const entities = (await this.sessionService.getSession(session.id))?.entities
    
    if (entities?.serviceId && entities?.date && entities?.time) {
      await this.sessionService.updateConversationState(session.id, 'confirming')
      
      return {
        message: `Perfect! Let me confirm your appointment:\n\n• Service: ${entities.serviceName}\n• Date: ${entities.dateDisplay || entities.date}\n• Time: ${data.time}\n\nShall I book this for you?`,
        sessionId: session.id,
        conversationState: 'confirming',
        nextAction: 'confirm',
        suggestions: ['Yes, book it', 'Change time', 'Change date', 'Cancel'],
        quickActions: [
          { label: 'Confirm Booking', action: 'confirm_booking' },
          { label: 'Modify', action: 'modify_booking' },
          { label: 'Cancel', action: 'cancel_booking' }
        ],
        bookingProgress: {
          step: 'confirmation',
          completedSteps: ['service', 'date', 'time'],
          nextSteps: ['confirmation'],
          collectedInfo: entities
        }
      }
    }
    
    return {
      message: `Got it, ${data.time}. I need a bit more information to complete your booking.`,
      sessionId: session.id,
      conversationState: 'collecting',
      nextAction: 'continue'
    }
  }

  private async confirmBooking(session: ConversationSession): Promise<AIReceptionistResponse> {
    try {
      const entities = session.entities
      if (!entities?.serviceId || !entities?.date || !entities?.time) {
        throw new Error('Missing required booking information')
      }
      
      // Create the booking
      const bookingResult = await this.appointmentService.createAppointment({
        storeId: session.storeId,
        customerId: session.customerId,
        serviceId: entities.serviceId,
        date: entities.date,
        time: entities.time,
        customerName: entities.customerName,
        customerPhone: entities.customerPhone,
        notes: entities.notes
      })
      
      await this.sessionService.updateConversationState(session.id, 'completed')
      
      return {
        message: `Excellent! Your appointment has been booked.\n\n${bookingResult.confirmationMessage}\n\n${bookingResult.nextSteps.join('\n')}`,
        sessionId: session.id,
        conversationState: 'completed',
        nextAction: 'complete',
        quickActions: [
          { label: 'Book Another', action: 'start_booking' },
          { label: 'View Details', action: 'view_booking', data: { bookingId: bookingResult.appointmentId } }
        ]
      }
    } catch (error) {
      console.error('Booking confirmation error:', error)
      return {
        message: "I'm sorry, there was an issue confirming your booking. Let me try again or connect you with our team.",
        sessionId: session.id,
        conversationState: 'error',
        nextAction: 'escalate',
        suggestions: ['Try again', 'Speak to someone', 'Call directly'],
        quickActions: [
          { label: 'Try Again', action: 'confirm_booking' },
          { label: 'Speak to Human', action: 'human_handoff' }
        ]
      }
    }
  }

  private async modifyBooking(session: ConversationSession): Promise<AIReceptionistResponse> {
    await this.sessionService.updateConversationState(session.id, 'collecting')
    
    return {
      message: "No problem! What would you like to change?",
      sessionId: session.id,
      conversationState: 'collecting',
      nextAction: 'continue',
      suggestions: ['Change time', 'Change date', 'Change service', 'Start over'],
      quickActions: [
        { label: 'Change Time', action: 'modify_time' },
        { label: 'Change Date', action: 'modify_date' },
        { label: 'Change Service', action: 'modify_service' },
        { label: 'Start Over', action: 'reset_conversation' }
      ]
    }
  }

  private async cancelBooking(session: ConversationSession): Promise<AIReceptionistResponse> {
    await this.sessionService.updateConversationState(session.id, 'greeting')
    
    return {
      message: "No worries! Your booking has been cancelled. Is there anything else I can help you with?",
      sessionId: session.id,
      conversationState: 'greeting',
      nextAction: 'continue',
      suggestions: ['Book different appointment', 'Check availability', 'Store information'],
      quickActions: [
        { label: 'Book Appointment', action: 'start_booking' },
        { label: 'View Services', action: 'show_services' }
      ]
    }
  }

  private async handleUnknownAction(
    session: ConversationSession,
    action: string
  ): Promise<AIReceptionistResponse> {
    return {
      message: "I'm not sure how to handle that action. How can I help you today?",
      sessionId: session.id,
      conversationState: session.status as any,
      nextAction: 'continue',
      suggestions: ['Book appointment', 'Check availability', 'Get help'],
      quickActions: [
        { label: 'Book Appointment', action: 'start_booking' },
        { label: 'Get Help', action: 'show_help' }
      ]
    }
  }

  private isSessionExpired(session: ConversationSession): boolean {
    if (!session.expiresAt) return false
    return DateTime.fromJSDate(session.expiresAt) < DateTime.now()
  }

  private mapConversationState(handlerState: string): 'greeting' | 'collecting' | 'confirming' | 'completed' | 'error' {
    switch (handlerState) {
      case 'greeting':
      case 'initial':
        return 'greeting'
      case 'collecting':
      case 'gathering':
        return 'collecting'
      case 'confirming':
      case 'confirmation':
        return 'confirming'
      case 'completed':
      case 'success':
        return 'completed'
      case 'error':
      case 'failed':
        return 'error'
      default:
        return 'collecting'
    }
  }

  private calculateIntentChanges(session: ConversationSession): number {
    // This would analyze message history for intent changes
    return session.messages?.length || 0 > 5 ? 1 : 0
  }

  private calculateAverageResponseTime(session: ConversationSession): number {
    // This would calculate based on message timestamps
    return 1500 // Default 1.5 seconds
  }
}