import { inject } from '@adonisjs/core'
import ZnStore from '#models/zn_store'
import ZnStoreService from '#models/store_service/zn_store_service'
import ZnStorePackage from '#models/store_service/zn_store_package'
import ZnUser from '#models/zn_user'
import CacheService from '#services/cache_service'
import { DateTime } from 'luxon'

export interface StoreContext {
  store: {
    id: string
    name: string
    address: string
    phoneNumber: string
    email: string
    workingHours: WorkingHours[]
    timezone: string
  }
  services: ServiceInfo[]
  packages: PackageInfo[]
  staff: StaffInfo[]
  policies: StorePolicy[]
  currentStatus: {
    isOpen: boolean
    nextOpenTime?: DateTime
    nextCloseTime?: DateTime
  }
}

export interface WorkingHours {
  dayOfWeek: string
  openTime: string
  closeTime: string
  isOpen: boolean
}

export interface ServiceInfo {
  id: string
  name: string
  price: number
  duration: number
  category: string
  description?: string
  isAvailable: boolean
}

export interface PackageInfo {
  id: string
  name: string
  price: number
  services: string[]
  description?: string
  isAvailable: boolean
}

export interface StaffInfo {
  id: string
  name: string
  specialties: string[]
  isAvailable: boolean
  workingHours: WorkingHours[]
}

export interface StorePolicy {
  type: 'cancellation' | 'booking' | 'payment' | 'general'
  title: string
  description: string
  rules: string[]
}

@inject()
export default class StoreContextService {
  constructor(private cacheService: CacheService) {}

  /**
   * Load comprehensive store context for AI conversations
   */
  async loadStoreContext(storeId: string): Promise<StoreContext> {
    const cacheKey = `store_context:${storeId}`
    
    // Try to get from cache first
    const cached = await this.cacheService.get(cacheKey)
    if (cached) {
      return JSON.parse(cached)
    }

    // Load fresh data
    const context = await this.buildStoreContext(storeId)
    
    // Cache for 30 minutes
    await this.cacheService.set(cacheKey, JSON.stringify(context), 1800)
    
    return context
  }

  /**
   * Build store context from database
   */
  private async buildStoreContext(storeId: string): Promise<StoreContext> {
    const store = await ZnStore.query()
      .where('id', storeId)
      .preload('thumbnail')
      .preload('country')
      .preload('state')
      .preload('city')
      .firstOrFail()

    const services = await this.loadServices(storeId)
    const packages = await this.loadPackages(storeId)
    const staff = await this.loadStaff(storeId)
    const policies = await this.loadPolicies(storeId)
    const currentStatus = this.calculateCurrentStatus(store.workingHour)

    return {
      store: {
        id: store.id,
        name: store.name,
        address: store.address,
        phoneNumber: store.phoneNumber,
        email: store.email,
        workingHours: this.formatWorkingHours(store.workingHour),
        timezone: 'America/New_York', // Default timezone, should be configurable
      },
      services,
      packages,
      staff,
      policies,
      currentStatus,
    }
  }

  /**
   * Load store services with availability
   */
  private async loadServices(storeId: string): Promise<ServiceInfo[]> {
    const services = await ZnStoreService.query()
      .where('storeId', storeId)
      .preload('categories')
      .preload('image')

    return services.map(service => ({
      id: service.id,
      name: service.name,
      price: service.price,
      duration: service.duration,
      category: service.categories?.[0]?.name || 'General',
      description: '', // Add description field to model if needed
      isAvailable: true, // Should check actual availability
    }))
  }

  /**
   * Load store packages with availability
   */
  private async loadPackages(storeId: string): Promise<PackageInfo[]> {
    const packages = await ZnStorePackage.query()
      .where('storeId', storeId)
      .preload('services')

    return packages.map(pkg => ({
      id: pkg.id,
      name: pkg.name,
      price: pkg.price,
      services: pkg.services?.map(s => s.name) || [],
      description: pkg.description || '',
      isAvailable: true, // Should check actual availability
    }))
  }

  /**
   * Load staff information
   */
  private async loadStaff(storeId: string): Promise<StaffInfo[]> {
    // This would need a staff model - for now return empty array
    // TODO: Implement when staff model is available
    return []
  }

  /**
   * Load store policies
   */
  private async loadPolicies(storeId: string): Promise<StorePolicy[]> {
    // This would need a policies model - for now return default policies
    return [
      {
        type: 'cancellation',
        title: 'Cancellation Policy',
        description: 'Appointments must be cancelled at least 24 hours in advance',
        rules: [
          'Cancellations within 24 hours may incur a fee',
          'No-shows will be charged the full service amount',
          'Rescheduling is allowed up to 2 hours before appointment',
        ],
      },
      {
        type: 'booking',
        title: 'Booking Policy',
        description: 'Guidelines for making appointments',
        rules: [
          'Appointments can be booked up to 30 days in advance',
          'Same-day appointments subject to availability',
          'Group bookings require advance notice',
        ],
      },
    ]
  }

  /**
   * Format working hours from database format
   */
  private formatWorkingHours(workingHour: any[]): WorkingHours[] {
    if (!workingHour || !Array.isArray(workingHour)) {
      return []
    }

    return workingHour.map(hour => ({
      dayOfWeek: hour.name,
      openTime: hour.from,
      closeTime: hour.to,
      isOpen: hour.isOpen,
    }))
  }

  /**
   * Calculate current store status
   */
  private calculateCurrentStatus(workingHour: any[]): {
    isOpen: boolean
    nextOpenTime?: DateTime
    nextCloseTime?: DateTime
  } {
    const now = DateTime.now()
    const currentDay = now.toFormat('cccc').toLowerCase()
    
    const todayHours = workingHour?.find(h => 
      h.name.toLowerCase() === currentDay
    )

    if (!todayHours || !todayHours.isOpen) {
      return {
        isOpen: false,
        nextOpenTime: this.findNextOpenTime(workingHour, now),
      }
    }

    const openTime = DateTime.fromFormat(todayHours.from, 'HH:mm')
    const closeTime = DateTime.fromFormat(todayHours.to, 'HH:mm')
    const currentTime = DateTime.fromFormat(now.toFormat('HH:mm'), 'HH:mm')

    const isOpen = currentTime >= openTime && currentTime <= closeTime

    return {
      isOpen,
      nextOpenTime: isOpen ? undefined : this.findNextOpenTime(workingHour, now),
      nextCloseTime: isOpen ? closeTime : undefined,
    }
  }

  /**
   * Find next opening time
   */
  private findNextOpenTime(workingHour: any[], from: DateTime): DateTime | undefined {
    // Simple implementation - find next day that's open
    for (let i = 1; i <= 7; i++) {
      const checkDate = from.plus({ days: i })
      const dayName = checkDate.toFormat('cccc').toLowerCase()
      
      const dayHours = workingHour?.find(h => 
        h.name.toLowerCase() === dayName
      )
      
      if (dayHours && dayHours.isOpen) {
        return checkDate.set({
          hour: parseInt(dayHours.from.split(':')[0]),
          minute: parseInt(dayHours.from.split(':')[1]),
        })
      }
    }
    
    return undefined
  }

  /**
   * Get store context summary for AI prompts
   */
  async getContextSummary(storeId: string): Promise<string> {
    const context = await this.loadStoreContext(storeId)
    
    const servicesList = context.services
      .map(s => `${s.name} ($${s.price}, ${s.duration}min)`)
      .join(', ')
    
    const workingHoursList = context.store.workingHours
      .filter(h => h.isOpen)
      .map(h => `${h.dayOfWeek}: ${h.openTime}-${h.closeTime}`)
      .join(', ')

    return `Store: ${context.store.name}
Address: ${context.store.address}
Phone: ${context.store.phoneNumber}
Hours: ${workingHoursList}
Services: ${servicesList}
Currently ${context.currentStatus.isOpen ? 'Open' : 'Closed'}`
  }

  /**
   * Invalidate store context cache
   */
  async invalidateCache(storeId: string): Promise<void> {
    const cacheKey = `store_context:${storeId}`
    await this.cacheService.delete(cacheKey)
  }

  /**
   * Check if store is currently open
   */
  async isStoreOpen(storeId: string): Promise<boolean> {
    const context = await this.loadStoreContext(storeId)
    return context.currentStatus.isOpen
  }

  /**
   * Get available services for booking
   */
  async getAvailableServices(storeId: string): Promise<ServiceInfo[]> {
    const context = await this.loadStoreContext(storeId)
    return context.services.filter(s => s.isAvailable)
  }

  /**
   * Get service by name (fuzzy matching)
   */
  async findServiceByName(storeId: string, serviceName: string): Promise<ServiceInfo | null> {
    const context = await this.loadStoreContext(storeId)
    
    // Exact match first
    let service = context.services.find(s => 
      s.name.toLowerCase() === serviceName.toLowerCase()
    )
    
    if (!service) {
      // Fuzzy match
      service = context.services.find(s => 
        s.name.toLowerCase().includes(serviceName.toLowerCase()) ||
        serviceName.toLowerCase().includes(s.name.toLowerCase())
      )
    }
    
    return service || null
  }
}