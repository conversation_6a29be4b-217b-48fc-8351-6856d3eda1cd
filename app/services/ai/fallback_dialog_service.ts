import { inject } from '@adonisjs/core'
import { DateTime } from 'luxon'
import SessionManagementService, { ConversationSession } from './session_management_service.js'
import StoreContextService from './store_context_service.js'
import CustomerContextService from './customer_context_service.js'
import BookingNLPService from './booking_nlp_service.js'

export interface FallbackResponse {
  message: string
  suggestions: string[]
  escalationLevel: 'low' | 'medium' | 'high' | 'critical'
  nextAction: 'retry' | 'clarify' | 'escalate' | 'reset' | 'end'
  contextHelp?: string[]
  quickActions?: QuickAction[]
}

export interface QuickAction {
  label: string
  action: string
  data?: any
}

export interface FallbackContext {
  sessionId: string
  storeId: string
  customerId?: string
  failureCount: number
  lastUserMessage: string
  conversationHistory: string[]
  detectedIssue: FallbackIssue
  userFrustrationLevel: number
}

export interface FallbackIssue {
  type: 'misunderstanding' | 'incomplete_info' | 'system_error' | 'out_of_scope' | 'ambiguous_intent' | 'technical_failure'
  description: string
  severity: 'low' | 'medium' | 'high'
  suggestedResolution: string[]
  requiresHuman: boolean
}

export interface ConversationRepair {
  strategy: 'rephrase' | 'simplify' | 'provide_examples' | 'ask_specific_questions' | 'offer_alternatives'
  repairMessage: string
  followUpQuestions: string[]
  contextClues: string[]
}

export interface EscalationTrigger {
  condition: string
  threshold: number
  action: 'human_handoff' | 'supervisor_alert' | 'callback_request' | 'email_support'
  priority: 'low' | 'medium' | 'high' | 'urgent'
}

@inject()
export default class FallbackDialogService {
  private readonly MAX_RETRY_ATTEMPTS = 3
  private readonly FRUSTRATION_THRESHOLD = 5
  private readonly ESCALATION_TRIGGERS: EscalationTrigger[] = [
    {
      condition: 'consecutive_failures',
      threshold: 3,
      action: 'human_handoff',
      priority: 'medium',
    },
    {
      condition: 'high_frustration',
      threshold: 7,
      action: 'supervisor_alert',
      priority: 'high',
    },
    {
      condition: 'system_errors',
      threshold: 2,
      action: 'callback_request',
      priority: 'urgent',
    },
  ]

  constructor(
    private sessionService: SessionManagementService,
    private storeContextService: StoreContextService,
    private customerContextService: CustomerContextService,
    private bookingNLPService: BookingNLPService
  ) {}

  /**
   * Handle fallback when AI fails to understand or process user input
   */
  async handleFallback(
    userMessage: string,
    sessionId: string,
    storeId: string,
    error?: any
  ): Promise<FallbackResponse> {
    try {
      // Get session and build context
      const session = await this.sessionService.getSession(sessionId)
      const context = await this.buildFallbackContext(
        userMessage,
        sessionId,
        storeId,
        session,
        error
      )

      // Detect the type of issue
      const issue = await this.detectIssue(userMessage, context, error)
      context.detectedIssue = issue

      // Update failure count and frustration level
      await this.updateFailureMetrics(sessionId, context)

      // Check if escalation is needed
      const escalationNeeded = this.checkEscalationTriggers(context)
      if (escalationNeeded) {
        return await this.handleEscalation(context, escalationNeeded)
      }

      // Generate appropriate fallback response
      const response = await this.generateFallbackResponse(context)

      // Log fallback for analysis
      await this.logFallbackEvent(context, response)

      return response
    } catch (fallbackError) {
      console.error('Fallback service error:', fallbackError)
      return this.getEmergencyFallback(storeId)
    }
  }

  /**
   * Attempt conversation repair based on detected issues
   */
  async repairConversation(
    sessionId: string,
    repairStrategy?: string
  ): Promise<ConversationRepair> {
    const session = await this.sessionService.getSession(sessionId)
    if (!session) {
      throw new Error('Session not found for conversation repair')
    }

    const context = await this.buildFallbackContext(
      session.lastMessage || '',
      sessionId,
      session.storeId,
      session
    )

    const strategy = repairStrategy || this.selectRepairStrategy(context)
    
    return await this.executeRepairStrategy(strategy, context)
  }

  /**
   * Provide contextual help based on user's current situation
   */
  async provideContextualHelp(
    sessionId: string,
    storeId: string
  ): Promise<{
    helpMessage: string
    availableActions: string[]
    examples: string[]
    shortcuts: QuickAction[]
  }> {
    const session = await this.sessionService.getSession(sessionId)
    const storeContext = await this.storeContextService.loadStoreContext(storeId)
    
    // Determine user's current context
    const currentState = session?.status || 'greeting'
    const collectedEntities = session?.entities || {}
    
    let helpMessage = ''
    let availableActions: string[] = []
    let examples: string[] = []
    let shortcuts: QuickAction[] = []

    switch (currentState) {
      case 'greeting':
        helpMessage = "I'm here to help you book appointments and answer questions about our services."
        availableActions = ['Book appointment', 'Check availability', 'View services', 'Store information']
        examples = [
          "I'd like to book a massage for tomorrow",
          "What are your hours?",
          "Do you have availability this weekend?"
        ]
        shortcuts = [
          { label: 'Book Now', action: 'start_booking' },
          { label: 'View Services', action: 'show_services' },
          { label: 'Store Hours', action: 'show_hours' }
        ]
        break

      case 'collecting':
        const missingInfo = this.identifyMissingInformation(collectedEntities)
        helpMessage = `I'm collecting information for your booking. I still need: ${missingInfo.join(', ')}.`
        availableActions = ['Provide missing information', 'Start over', 'Get help']
        examples = this.generateExamplesForMissingInfo(missingInfo, storeContext)
        shortcuts = this.generateShortcutsForMissingInfo(missingInfo, storeContext)
        break

      case 'confirming':
        helpMessage = "I have your booking details and I'm ready to confirm your appointment."
        availableActions = ['Confirm booking', 'Modify details', 'Cancel']
        examples = ['Yes, book it', 'Change the time to 2 PM', 'Actually, let me think about it']
        shortcuts = [
          { label: 'Confirm', action: 'confirm_booking' },
          { label: 'Modify', action: 'modify_booking' },
          { label: 'Cancel', action: 'cancel_booking' }
        ]
        break

      default:
        helpMessage = "I can help you with booking appointments, checking availability, or answering questions about our services."
        availableActions = ['Book appointment', 'Check availability', 'Get information']
        examples = ['Book a haircut', 'Are you open tomorrow?', 'What services do you offer?']
    }

    return {
      helpMessage,
      availableActions,
      examples,
      shortcuts
    }
  }

  /**
   * Reset conversation when user wants to start over
   */
  async resetConversation(sessionId: string, storeId: string): Promise<FallbackResponse> {
    // Clear session data but keep basic info
    await this.sessionService.updateConversationState(sessionId, 'greeting')
    await this.sessionService.resetRetryCount(sessionId)
    
    // Clear collected entities except customer info
    const session = await this.sessionService.getSession(sessionId)
    if (session) {
      const preservedEntities = {
        customerId: session.entities?.customerId,
        customerName: session.entities?.customerName,
        customerPhone: session.entities?.customerPhone,
      }
      
      // Reset entities but preserve customer info
      await this.sessionService.addEntities(sessionId, preservedEntities)
    }

    const storeContext = await this.storeContextService.loadStoreContext(storeId)
    
    return {
      message: `No problem! Let's start fresh. I'm here to help you with ${storeContext.name}. What can I do for you today?`,
      suggestions: ['Book appointment', 'Check availability', 'View services', 'Store information'],
      escalationLevel: 'low',
      nextAction: 'retry',
      quickActions: [
        { label: 'Book Appointment', action: 'start_booking' },
        { label: 'View Services', action: 'show_services' },
        { label: 'Store Hours', action: 'show_hours' }
      ]
    }
  }

  /**
   * Private helper methods
   */
  private async buildFallbackContext(
    userMessage: string,
    sessionId: string,
    storeId: string,
    session?: ConversationSession | null,
    error?: any
  ): Promise<FallbackContext> {
    const conversationHistory = session?.messages?.map(m => m.content) || []
    const failureCount = session?.retryCount || 0
    
    return {
      sessionId,
      storeId,
      customerId: session?.customerId,
      failureCount,
      lastUserMessage: userMessage,
      conversationHistory,
      detectedIssue: {
        type: 'misunderstanding',
        description: 'Unknown issue',
        severity: 'low',
        suggestedResolution: [],
        requiresHuman: false
      },
      userFrustrationLevel: this.calculateFrustrationLevel(failureCount, conversationHistory)
    }
  }

  private async detectIssue(
    userMessage: string,
    context: FallbackContext,
    error?: any
  ): Promise<FallbackIssue> {
    // System error detection
    if (error) {
      return {
        type: 'system_error',
        description: `System error: ${error.message || 'Unknown error'}`,
        severity: 'high',
        suggestedResolution: ['Retry request', 'Contact support'],
        requiresHuman: error.code === 'CRITICAL_ERROR'
      }
    }

    // Analyze user message for common issues
    const messageLength = userMessage.trim().length
    const hasQuestionMarks = (userMessage.match(/\?/g) || []).length
    const hasMultipleRequests = this.detectMultipleIntents(userMessage)
    
    // Very short or unclear messages
    if (messageLength < 3) {
      return {
        type: 'incomplete_info',
        description: 'Message too short or unclear',
        severity: 'low',
        suggestedResolution: ['Ask for more details', 'Provide examples'],
        requiresHuman: false
      }
    }

    // Multiple questions or complex requests
    if (hasQuestionMarks > 2 || hasMultipleRequests) {
      return {
        type: 'ambiguous_intent',
        description: 'Multiple or complex requests detected',
        severity: 'medium',
        suggestedResolution: ['Break down request', 'Ask for prioritization'],
        requiresHuman: false
      }
    }

    // Out of scope detection
    const isOutOfScope = await this.detectOutOfScope(userMessage, context.storeId)
    if (isOutOfScope) {
      return {
        type: 'out_of_scope',
        description: 'Request outside of booking/service scope',
        severity: 'medium',
        suggestedResolution: ['Redirect to appropriate channel', 'Provide contact information'],
        requiresHuman: true
      }
    }

    // High frustration detection
    if (context.userFrustrationLevel > this.FRUSTRATION_THRESHOLD) {
      return {
        type: 'misunderstanding',
        description: 'User showing signs of frustration',
        severity: 'high',
        suggestedResolution: ['Offer human assistance', 'Simplify interaction'],
        requiresHuman: true
      }
    }

    // Default misunderstanding
    return {
      type: 'misunderstanding',
      description: 'Unable to understand user intent',
      severity: 'medium',
      suggestedResolution: ['Ask for clarification', 'Provide examples'],
      requiresHuman: false
    }
  }

  private async updateFailureMetrics(sessionId: string, context: FallbackContext): Promise<void> {
    await this.sessionService.incrementRetryCount(sessionId)
    
    // Update session metadata with failure info
    await this.sessionService.addMetadata(sessionId, {
      lastFailureType: context.detectedIssue.type,
      lastFailureTime: DateTime.now().toISO(),
      frustrationLevel: context.userFrustrationLevel
    })
  }

  private checkEscalationTriggers(context: FallbackContext): EscalationTrigger | null {
    for (const trigger of this.ESCALATION_TRIGGERS) {
      let shouldEscalate = false
      
      switch (trigger.condition) {
        case 'consecutive_failures':
          shouldEscalate = context.failureCount >= trigger.threshold
          break
        case 'high_frustration':
          shouldEscalate = context.userFrustrationLevel >= trigger.threshold
          break
        case 'system_errors':
          shouldEscalate = context.detectedIssue.type === 'system_error' && 
                          context.failureCount >= trigger.threshold
          break
      }
      
      if (shouldEscalate) {
        return trigger
      }
    }
    
    return null
  }

  private async handleEscalation(
    context: FallbackContext,
    trigger: EscalationTrigger
  ): Promise<FallbackResponse> {
    const storeContext = await this.storeContextService.loadStoreContext(context.storeId)
    
    switch (trigger.action) {
      case 'human_handoff':
        return {
          message: "I'd like to connect you with one of our team members who can better assist you. Please hold on while I transfer you, or you can call us directly.",
          suggestions: [`Call ${storeContext.phoneNumber}`, 'Wait for transfer', 'Try again later'],
          escalationLevel: 'high',
          nextAction: 'escalate',
          quickActions: [
            { label: 'Call Now', action: 'call_store', data: { phone: storeContext.phoneNumber } },
            { label: 'Request Callback', action: 'request_callback' }
          ]
        }
      
      case 'supervisor_alert':
        // Alert supervisor but continue conversation
        await this.alertSupervisor(context, trigger)
        return {
          message: "I understand this is frustrating. Let me try a different approach to help you, or I can arrange for someone to call you back.",
          suggestions: ['Try different approach', 'Request callback', 'Speak to manager'],
          escalationLevel: 'high',
          nextAction: 'clarify',
          quickActions: [
            { label: 'Request Callback', action: 'request_callback' },
            { label: 'Speak to Manager', action: 'escalate_to_manager' }
          ]
        }
      
      case 'callback_request':
        return {
          message: "I'm experiencing some technical difficulties. Would you like me to have someone call you back to assist with your booking?",
          suggestions: ['Request callback', 'Try again later', 'Call directly'],
          escalationLevel: 'critical',
          nextAction: 'escalate',
          quickActions: [
            { label: 'Request Callback', action: 'request_callback' },
            { label: 'Call Store', action: 'call_store', data: { phone: storeContext.phoneNumber } }
          ]
        }
      
      default:
        return this.getEmergencyFallback(context.storeId)
    }
  }

  private async generateFallbackResponse(context: FallbackContext): Promise<FallbackResponse> {
    const { detectedIssue, userFrustrationLevel } = context
    
    switch (detectedIssue.type) {
      case 'incomplete_info':
        return {
          message: "I'd like to help you, but I need a bit more information. Could you tell me more about what you're looking for?",
          suggestions: ['Book appointment', 'Check availability', 'Get information', 'Speak to someone'],
          escalationLevel: 'low',
          nextAction: 'clarify',
          contextHelp: [
            "Try: 'I want to book a massage'",
            "Or: 'What are your hours?'",
            "Or: 'Do you have availability tomorrow?'"
          ]
        }
      
      case 'ambiguous_intent':
        return {
          message: "I see you have several questions. Let me help you one at a time. What's the most important thing I can help you with first?",
          suggestions: ['Book appointment', 'Check availability', 'Get information'],
          escalationLevel: 'medium',
          nextAction: 'clarify',
          contextHelp: [
            "Let's focus on one thing at a time",
            "I can help with booking, availability, or information"
          ]
        }
      
      case 'out_of_scope':
        const storeContext = await this.storeContextService.loadStoreContext(context.storeId)
        return {
          message: `I specialize in helping with appointments and service information for ${storeContext.name}. For other inquiries, you might want to speak with our team directly.`,
          suggestions: [`Call ${storeContext.phoneNumber}`, 'Book appointment', 'View services'],
          escalationLevel: 'medium',
          nextAction: 'clarify',
          quickActions: [
            { label: 'Call Store', action: 'call_store', data: { phone: storeContext.phoneNumber } },
            { label: 'Book Appointment', action: 'start_booking' }
          ]
        }
      
      case 'system_error':
        return {
          message: "I'm experiencing some technical difficulties. Let me try again, or you can contact us directly for immediate assistance.",
          suggestions: ['Try again', 'Contact support', 'Call directly'],
          escalationLevel: 'high',
          nextAction: 'retry',
          quickActions: [
            { label: 'Try Again', action: 'retry' },
            { label: 'Contact Support', action: 'contact_support' }
          ]
        }
      
      default:
        if (userFrustrationLevel > this.FRUSTRATION_THRESHOLD) {
          return {
            message: "I apologize for the confusion. Would you prefer to speak with one of our team members directly? They can help you right away.",
            suggestions: ['Speak to human', 'Try again', 'Call directly'],
            escalationLevel: 'high',
            nextAction: 'escalate',
            quickActions: [
              { label: 'Speak to Human', action: 'human_handoff' },
              { label: 'Start Over', action: 'reset_conversation' }
            ]
          }
        }
        
        return {
          message: "I'm not sure I understood that correctly. Could you rephrase your request? I'm here to help with appointments and service information.",
          suggestions: ['Book appointment', 'Check availability', 'Get help', 'Start over'],
          escalationLevel: 'low',
          nextAction: 'clarify',
          contextHelp: [
            "Try being more specific",
            "Use simple phrases",
            "Ask one question at a time"
          ]
        }
    }
  }

  private selectRepairStrategy(context: FallbackContext): string {
    const { detectedIssue, failureCount, userFrustrationLevel } = context
    
    if (userFrustrationLevel > this.FRUSTRATION_THRESHOLD) {
      return 'offer_alternatives'
    }
    
    if (failureCount > 2) {
      return 'simplify'
    }
    
    switch (detectedIssue.type) {
      case 'incomplete_info':
        return 'ask_specific_questions'
      case 'ambiguous_intent':
        return 'provide_examples'
      case 'misunderstanding':
        return 'rephrase'
      default:
        return 'provide_examples'
    }
  }

  private async executeRepairStrategy(
    strategy: string,
    context: FallbackContext
  ): Promise<ConversationRepair> {
    const storeContext = await this.storeContextService.loadStoreContext(context.storeId)
    
    switch (strategy) {
      case 'rephrase':
        return {
          strategy: 'rephrase',
          repairMessage: "Let me try asking this differently. What would you like to do today?",
          followUpQuestions: [
            "Are you looking to book an appointment?",
            "Do you need information about our services?",
            "Would you like to check our availability?"
          ],
          contextClues: ['booking', 'information', 'availability']
        }
      
      case 'simplify':
        return {
          strategy: 'simplify',
          repairMessage: "Let's keep this simple. I can help you with three main things:",
          followUpQuestions: [
            "1. Book an appointment",
            "2. Check availability", 
            "3. Get information about services"
          ],
          contextClues: ['book', 'check', 'information']
        }
      
      case 'provide_examples':
        return {
          strategy: 'provide_examples',
          repairMessage: "Here are some examples of how you can ask me for help:",
          followUpQuestions: [
            "'I want to book a massage for tomorrow'",
            "'What are your hours?'",
            "'Do you have availability this weekend?'"
          ],
          contextClues: ['book', 'hours', 'availability']
        }
      
      case 'ask_specific_questions':
        return {
          strategy: 'ask_specific_questions',
          repairMessage: "Let me ask you some specific questions to help you better:",
          followUpQuestions: [
            "What service are you interested in?",
            "What date works for you?",
            "What time do you prefer?"
          ],
          contextClues: ['service', 'date', 'time']
        }
      
      case 'offer_alternatives':
        return {
          strategy: 'offer_alternatives',
          repairMessage: "I want to make sure you get the help you need. Here are your options:",
          followUpQuestions: [
            "Continue with me using simple questions",
            "Speak with a team member directly",
            `Call us at ${storeContext.phoneNumber}`
          ],
          contextClues: ['continue', 'speak', 'call']
        }
      
      default:
        return {
          strategy: 'provide_examples',
          repairMessage: "I'm here to help! Try asking me something like:",
          followUpQuestions: [
            "'Book an appointment'",
            "'Check availability'",
            "'What services do you offer?'"
          ],
          contextClues: ['book', 'check', 'services']
        }
    }
  }

  private calculateFrustrationLevel(failureCount: number, conversationHistory: string[]): number {
    let frustrationLevel = failureCount * 2 // Base frustration from failures
    
    // Analyze conversation for frustration indicators
    const recentMessages = conversationHistory.slice(-5)
    const frustrationWords = ['frustrated', 'annoyed', 'confused', 'help', 'wrong', 'stupid', 'terrible']
    const urgencyWords = ['urgent', 'asap', 'immediately', 'now', 'quickly']
    const repetitionWords = ['again', 'repeat', 'told you', 'already said']
    
    recentMessages.forEach(message => {
      const lowerMessage = message.toLowerCase()
      
      frustrationWords.forEach(word => {
        if (lowerMessage.includes(word)) frustrationLevel += 1
      })
      
      urgencyWords.forEach(word => {
        if (lowerMessage.includes(word)) frustrationLevel += 0.5
      })
      
      repetitionWords.forEach(word => {
        if (lowerMessage.includes(word)) frustrationLevel += 1.5
      })
      
      // Check for caps (shouting)
      if (message === message.toUpperCase() && message.length > 5) {
        frustrationLevel += 2
      }
      
      // Check for excessive punctuation
      if ((message.match(/[!?]/g) || []).length > 2) {
        frustrationLevel += 1
      }
    })
    
    return Math.min(10, Math.max(0, frustrationLevel)) // Cap between 0-10
  }

  private detectMultipleIntents(message: string): boolean {
    const intentKeywords = [
      ['book', 'appointment', 'schedule'],
      ['cancel', 'remove', 'delete'],
      ['reschedule', 'change', 'move'],
      ['check', 'availability', 'open'],
      ['price', 'cost', 'how much'],
      ['hours', 'open', 'close'],
      ['location', 'address', 'where']
    ]
    
    let intentCount = 0
    const lowerMessage = message.toLowerCase()
    
    intentKeywords.forEach(keywords => {
      if (keywords.some(keyword => lowerMessage.includes(keyword))) {
        intentCount++
      }
    })
    
    return intentCount > 1
  }

  private async detectOutOfScope(message: string, storeId: string): Promise<boolean> {
    const outOfScopeKeywords = [
      'weather', 'news', 'politics', 'sports', 'recipe', 'directions',
      'technical support', 'billing', 'refund', 'complaint', 'legal',
      'medical advice', 'diagnosis', 'prescription'
    ]
    
    const lowerMessage = message.toLowerCase()
    return outOfScopeKeywords.some(keyword => lowerMessage.includes(keyword))
  }

  private identifyMissingInformation(collectedEntities: Record<string, any>): string[] {
    const requiredFields = ['services', 'date', 'time']
    const missing: string[] = []
    
    requiredFields.forEach(field => {
      if (!collectedEntities[field]) {
        missing.push(field)
      }
    })
    
    return missing
  }

  private generateExamplesForMissingInfo(missingInfo: string[], storeContext: any): string[] {
    const examples: string[] = []
    
    missingInfo.forEach(info => {
      switch (info) {
        case 'services':
          if (storeContext.services?.length > 0) {
            examples.push(`"${storeContext.services[0].name}"`)
          } else {
            examples.push('"Massage"')
          }
          break
        case 'date':
          examples.push('"Tomorrow"', '"This Friday"', '"Next week"')
          break
        case 'time':
          examples.push('"2 PM"', '"Morning"', '"After 3 PM"')
          break
      }
    })
    
    return examples
  }

  private generateShortcutsForMissingInfo(missingInfo: string[], storeContext: any): QuickAction[] {
    const shortcuts: QuickAction[] = []
    
    missingInfo.forEach(info => {
      switch (info) {
        case 'services':
          if (storeContext.services?.length > 0) {
            storeContext.services.slice(0, 3).forEach((service: any) => {
              shortcuts.push({
                label: service.name,
                action: 'select_service',
                data: { serviceId: service.id, serviceName: service.name }
              })
            })
          }
          break
        case 'date':
          shortcuts.push(
            { label: 'Today', action: 'select_date', data: { date: 'today' } },
            { label: 'Tomorrow', action: 'select_date', data: { date: 'tomorrow' } },
            { label: 'This Weekend', action: 'select_date', data: { date: 'weekend' } }
          )
          break
        case 'time':
          shortcuts.push(
            { label: 'Morning', action: 'select_time', data: { time: 'morning' } },
            { label: 'Afternoon', action: 'select_time', data: { time: 'afternoon' } },
            { label: 'Evening', action: 'select_time', data: { time: 'evening' } }
          )
          break
      }
    })
    
    return shortcuts
  }

  private async alertSupervisor(context: FallbackContext, trigger: EscalationTrigger): Promise<void> {
    // This would integrate with alerting system
    console.log('Supervisor alert:', {
      sessionId: context.sessionId,
      storeId: context.storeId,
      trigger: trigger.condition,
      priority: trigger.priority,
      frustrationLevel: context.userFrustrationLevel,
      failureCount: context.failureCount
    })
  }

  private async logFallbackEvent(
    context: FallbackContext,
    response: FallbackResponse
  ): Promise<void> {
    // This would log to analytics/monitoring system
    console.log('Fallback event:', {
      sessionId: context.sessionId,
      storeId: context.storeId,
      issueType: context.detectedIssue.type,
      escalationLevel: response.escalationLevel,
      userMessage: context.lastUserMessage,
      timestamp: DateTime.now().toISO()
    })
  }

  private getEmergencyFallback(storeId: string): FallbackResponse {
    return {
      message: "I'm experiencing technical difficulties. Please contact us directly for immediate assistance.",
      suggestions: ['Call directly', 'Try again later', 'Contact support'],
      escalationLevel: 'critical',
      nextAction: 'escalate',
      quickActions: [
        { label: 'Contact Support', action: 'contact_support' },
        { label: 'Try Again', action: 'retry' }
      ]
    }
  }
}