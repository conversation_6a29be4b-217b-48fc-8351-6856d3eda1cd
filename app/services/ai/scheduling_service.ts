import { inject } from '@adonisjs/core'
import { DateTime } from 'luxon'
import ZnAppointment from '#models/zn_appointment'
import ZnStore from '#models/zn_store'
import ZnUser from '#models/zn_user'
import ZnStoreService from '#models/zn_store_service'
import StoreContextService, { WorkingHours } from './store_context_service.js'
import CustomerContextService from './customer_context_service.js'

export interface SchedulingRequest {
  storeId: string
  serviceIds: string[]
  preferredDate?: DateTime
  preferredTime?: DateTime
  staffId?: string
  customerId?: string
  duration?: number
  flexibility: 'strict' | 'moderate' | 'flexible'
}

export interface SchedulingResult {
  availableSlots: ScheduleSlot[]
  recommendedSlot?: ScheduleSlot
  alternatives: ScheduleSlot[]
  constraints: SchedulingConstraint[]
}

export interface ScheduleSlot {
  startTime: DateTime
  endTime: DateTime
  staffId?: string
  staffName?: string
  services: string[]
  totalDuration: number
  totalPrice: number
  confidence: number
  reasons: string[]
}

export interface SchedulingConstraint {
  type: 'store_hours' | 'staff_availability' | 'service_duration' | 'customer_preference' | 'existing_appointment'
  description: string
  severity: 'error' | 'warning' | 'info'
  affectedTimeRange?: { start: DateTime; end: DateTime }
}

export interface StaffAvailability {
  staffId: string
  staffName: string
  availableSlots: TimeRange[]
  busySlots: TimeRange[]
  preferences: StaffPreferences
}

export interface TimeRange {
  start: DateTime
  end: DateTime
  type?: 'available' | 'busy' | 'break' | 'blocked'
  reason?: string
}

export interface StaffPreferences {
  preferredHours: { start: string; end: string }
  specialties: string[]
  maxConsecutiveHours: number
  breakDuration: number
}

export interface AvailabilityMatrix {
  date: DateTime
  timeSlots: TimeSlotInfo[]
  staffAvailability: Map<string, boolean[]>
  serviceRequirements: Map<string, ServiceRequirement>
}

export interface TimeSlotInfo {
  time: DateTime
  duration: number
  available: boolean
  conflicts: string[]
  score: number
}

export interface ServiceRequirement {
  serviceId: string
  duration: number
  requiredStaff?: string[]
  setupTime: number
  cleanupTime: number
}

@inject()
export default class SchedulingService {
  private readonly SLOT_INTERVAL = 15 // minutes
  private readonly MAX_SEARCH_DAYS = 30
  private readonly BUFFER_TIME = 15 // minutes between appointments

  constructor(
    private storeContextService: StoreContextService,
    private customerContextService: CustomerContextService
  ) {}

  /**
   * Find available time slots based on scheduling request
   */
  async findAvailableSlots(request: SchedulingRequest): Promise<SchedulingResult> {
    // Load store context and constraints
    const storeContext = await this.storeContextService.loadStoreContext(request.storeId)
    const constraints = await this.analyzeConstraints(request)
    
    // Get service requirements
    const serviceRequirements = await this.getServiceRequirements(request.serviceIds)
    const totalDuration = serviceRequirements.reduce((sum, req) => sum + req.duration, 0)
    
    // Generate availability matrix
    const searchStartDate = request.preferredDate || DateTime.now()
    const availabilityMatrix = await this.generateAvailabilityMatrix(
      request.storeId,
      searchStartDate,
      totalDuration,
      request.staffId
    )
    
    // Find available slots
    const availableSlots = await this.findSlotsInMatrix(
      availabilityMatrix,
      serviceRequirements,
      request
    )
    
    // Score and rank slots
    const scoredSlots = await this.scoreSlots(availableSlots, request)
    
    // Generate alternatives
    const alternatives = await this.generateAlternatives(request, scoredSlots)
    
    return {
      availableSlots: scoredSlots.slice(0, 10), // Top 10 slots
      recommendedSlot: scoredSlots[0],
      alternatives: alternatives.slice(0, 5),
      constraints,
    }
  }

  /**
   * Check specific time slot availability
   */
  async checkSlotAvailability(
    storeId: string,
    startTime: DateTime,
    duration: number,
    staffId?: string
  ): Promise<{
    available: boolean
    conflicts: string[]
    suggestions: DateTime[]
  }> {
    const endTime = startTime.plus({ minutes: duration })
    const conflicts: string[] = []
    
    // Check store hours
    const storeContext = await this.storeContextService.loadStoreContext(storeId)
    const isWithinHours = this.isWithinStoreHours(startTime, storeContext.workingHours)
    
    if (!isWithinHours) {
      conflicts.push('Outside store operating hours')
    }
    
    // Check existing appointments
    const existingAppointments = await this.getExistingAppointments(
      storeId,
      startTime,
      endTime,
      staffId
    )
    
    if (existingAppointments.length > 0) {
      conflicts.push(`${existingAppointments.length} conflicting appointment(s)`)
    }
    
    // Check staff availability
    if (staffId) {
      const staffAvailable = await this.checkStaffAvailability(staffId, startTime, endTime)
      if (!staffAvailable) {
        conflicts.push('Staff not available')
      }
    }
    
    const available = conflicts.length === 0
    
    // Generate suggestions if not available
    let suggestions: DateTime[] = []
    if (!available) {
      suggestions = await this.findNearbyAvailableSlots(
        storeId,
        startTime,
        duration,
        staffId
      )
    }
    
    return {
      available,
      conflicts,
      suggestions,
    }
  }

  /**
   * Get staff availability for a specific date range
   */
  async getStaffAvailability(
    storeId: string,
    startDate: DateTime,
    endDate: DateTime,
    staffId?: string
  ): Promise<StaffAvailability[]> {
    // Get store staff
    const staff = await this.getStoreStaff(storeId, staffId)
    const availability: StaffAvailability[] = []
    
    for (const member of staff) {
      const staffAvailability = await this.calculateStaffAvailability(
        member.id,
        startDate,
        endDate
      )
      
      availability.push(staffAvailability)
    }
    
    return availability
  }

  /**
   * Optimize schedule for multiple appointments
   */
  async optimizeSchedule(
    storeId: string,
    appointments: SchedulingRequest[],
    date: DateTime
  ): Promise<{
    optimizedSlots: ScheduleSlot[]
    efficiency: number
    conflicts: SchedulingConstraint[]
  }> {
    // Sort appointments by priority/flexibility
    const sortedAppointments = this.sortAppointmentsByPriority(appointments)
    
    // Generate availability matrix for the day
    const matrix = await this.generateDailyAvailabilityMatrix(storeId, date)
    
    // Use greedy algorithm to assign slots
    const optimizedSlots: ScheduleSlot[] = []
    const conflicts: SchedulingConstraint[] = []
    
    for (const request of sortedAppointments) {
      const result = await this.findBestSlotInMatrix(matrix, request)
      
      if (result.slot) {
        optimizedSlots.push(result.slot)
        this.markSlotAsUsed(matrix, result.slot)
      } else {
        conflicts.push({
          type: 'existing_appointment',
          description: `Cannot schedule appointment for ${request.serviceIds.join(', ')}`,
          severity: 'error',
        })
      }
    }
    
    // Calculate efficiency score
    const efficiency = this.calculateScheduleEfficiency(optimizedSlots, matrix)
    
    return {
      optimizedSlots,
      efficiency,
      conflicts,
    }
  }

  /**
   * Get recommended time slots based on customer preferences
   */
  async getRecommendedSlots(
    storeId: string,
    customerId: string,
    serviceIds: string[],
    preferredDate?: DateTime
  ): Promise<ScheduleSlot[]> {
    // Get customer context and preferences
    const customerContext = await this.customerContextService.loadCustomerContext(
      customerId,
      storeId
    )
    
    // Analyze customer's booking history
    const preferredTimes = this.analyzeCustomerTimePreferences(customerContext)
    
    // Find slots that match preferences
    const request: SchedulingRequest = {
      storeId,
      serviceIds,
      preferredDate: preferredDate || DateTime.now().plus({ days: 1 }),
      customerId,
      flexibility: 'moderate',
    }
    
    const result = await this.findAvailableSlots(request)
    
    // Boost scores for slots that match customer preferences
    return result.availableSlots.map(slot => ({
      ...slot,
      confidence: this.adjustConfidenceForCustomerPreferences(
        slot,
        preferredTimes,
        customerContext
      ),
    })).sort((a, b) => b.confidence - a.confidence)
  }

  /**
   * Private helper methods
   */
  private async analyzeConstraints(request: SchedulingRequest): Promise<SchedulingConstraint[]> {
    const constraints: SchedulingConstraint[] = []
    
    // Store hours constraint
    const storeContext = await this.storeContextService.loadStoreContext(request.storeId)
    constraints.push({
      type: 'store_hours',
      description: `Store operates ${this.formatWorkingHours(storeContext.workingHours)}`,
      severity: 'info',
    })
    
    // Service duration constraint
    const serviceRequirements = await this.getServiceRequirements(request.serviceIds)
    const totalDuration = serviceRequirements.reduce((sum, req) => sum + req.duration, 0)
    
    if (totalDuration > 240) { // 4 hours
      constraints.push({
        type: 'service_duration',
        description: `Long appointment duration: ${totalDuration} minutes`,
        severity: 'warning',
      })
    }
    
    return constraints
  }

  private async getServiceRequirements(serviceIds: string[]): Promise<ServiceRequirement[]> {
    const services = await ZnStoreService.query().whereIn('id', serviceIds)
    
    return services.map(service => ({
      serviceId: service.id,
      duration: service.duration || 60,
      setupTime: 5, // Default setup time
      cleanupTime: 5, // Default cleanup time
    }))
  }

  private async generateAvailabilityMatrix(
    storeId: string,
    startDate: DateTime,
    duration: number,
    staffId?: string
  ): Promise<AvailabilityMatrix[]> {
    const matrices: AvailabilityMatrix[] = []
    
    for (let i = 0; i < this.MAX_SEARCH_DAYS; i++) {
      const date = startDate.plus({ days: i })
      const matrix = await this.generateDailyAvailabilityMatrix(storeId, date, staffId)
      matrices.push(matrix)
    }
    
    return matrices
  }

  private async generateDailyAvailabilityMatrix(
    storeId: string,
    date: DateTime,
    staffId?: string
  ): Promise<AvailabilityMatrix> {
    const storeContext = await this.storeContextService.loadStoreContext(storeId)
    const dayOfWeek = date.weekdayLong?.toLowerCase()
    const storeHours = storeContext.workingHours.find(h => h.day === dayOfWeek)
    
    const timeSlots: TimeSlotInfo[] = []
    const staffAvailability = new Map<string, boolean[]>()
    
    if (storeHours && storeHours.isOpen) {
      // Generate time slots for the day
      const startTime = date.set({
        hour: parseInt(storeHours.openTime.split(':')[0]),
        minute: parseInt(storeHours.openTime.split(':')[1]),
      })
      
      const endTime = date.set({
        hour: parseInt(storeHours.closeTime.split(':')[0]),
        minute: parseInt(storeHours.closeTime.split(':')[1]),
      })
      
      let currentTime = startTime
      while (currentTime < endTime) {
        const conflicts = await this.getSlotConflicts(storeId, currentTime, staffId)
        
        timeSlots.push({
          time: currentTime,
          duration: this.SLOT_INTERVAL,
          available: conflicts.length === 0,
          conflicts,
          score: this.calculateSlotScore(currentTime, conflicts),
        })
        
        currentTime = currentTime.plus({ minutes: this.SLOT_INTERVAL })
      }
    }
    
    return {
      date,
      timeSlots,
      staffAvailability,
      serviceRequirements: new Map(),
    }
  }

  private async findSlotsInMatrix(
    matrices: AvailabilityMatrix[],
    serviceRequirements: ServiceRequirement[],
    request: SchedulingRequest
  ): Promise<ScheduleSlot[]> {
    const slots: ScheduleSlot[] = []
    const totalDuration = serviceRequirements.reduce((sum, req) => sum + req.duration, 0)
    
    for (const matrix of matrices) {
      for (let i = 0; i < matrix.timeSlots.length; i++) {
        const startSlot = matrix.timeSlots[i]
        
        if (!startSlot.available) continue
        
        // Check if we can fit the entire appointment
        const slotsNeeded = Math.ceil(totalDuration / this.SLOT_INTERVAL)
        let canFit = true
        
        for (let j = i; j < i + slotsNeeded && j < matrix.timeSlots.length; j++) {
          if (!matrix.timeSlots[j].available) {
            canFit = false
            break
          }
        }
        
        if (canFit) {
          const endTime = startSlot.time.plus({ minutes: totalDuration })
          
          slots.push({
            startTime: startSlot.time,
            endTime,
            services: request.serviceIds,
            totalDuration,
            totalPrice: await this.calculateSlotPrice(request.serviceIds),
            confidence: 0.8, // Will be adjusted later
            reasons: ['Available slot found'],
          })
        }
      }
    }
    
    return slots
  }

  private async scoreSlots(
    slots: ScheduleSlot[],
    request: SchedulingRequest
  ): Promise<ScheduleSlot[]> {
    const scoredSlots = await Promise.all(
      slots.map(async slot => {
        let score = 0.5 // Base score
        
        // Time preference scoring
        if (request.preferredTime) {
          const timeDiff = Math.abs(
            slot.startTime.diff(request.preferredTime, 'minutes').minutes
          )
          score += Math.max(0, (240 - timeDiff) / 240) * 0.3 // Max 0.3 for time match
        }
        
        // Date preference scoring
        if (request.preferredDate) {
          const daysDiff = Math.abs(
            slot.startTime.startOf('day').diff(request.preferredDate.startOf('day'), 'days').days
          )
          score += Math.max(0, (7 - daysDiff) / 7) * 0.2 // Max 0.2 for date match
        }
        
        // Customer preference scoring
        if (request.customerId) {
          const customerScore = await this.getCustomerPreferenceScore(
            request.customerId,
            slot.startTime
          )
          score += customerScore * 0.3
        }
        
        // Avoid peak hours penalty
        const hour = slot.startTime.hour
        if (hour >= 12 && hour <= 14) { // Lunch time
          score -= 0.1
        }
        
        return {
          ...slot,
          confidence: Math.min(1, Math.max(0, score)),
        }
      })
    )
    
    return scoredSlots.sort((a, b) => b.confidence - a.confidence)
  }

  private async generateAlternatives(
    request: SchedulingRequest,
    availableSlots: ScheduleSlot[]
  ): Promise<ScheduleSlot[]> {
    const alternatives: ScheduleSlot[] = []
    
    // If flexibility allows, suggest different days
    if (request.flexibility !== 'strict' && request.preferredDate) {
      const alternativeDates = [
        request.preferredDate.plus({ days: 1 }),
        request.preferredDate.minus({ days: 1 }),
        request.preferredDate.plus({ days: 7 }),
      ]
      
      for (const altDate of alternativeDates) {
        const altRequest = { ...request, preferredDate: altDate }
        const altResult = await this.findAvailableSlots(altRequest)
        
        if (altResult.availableSlots.length > 0) {
          alternatives.push(...altResult.availableSlots.slice(0, 2))
        }
      }
    }
    
    return alternatives
  }

  private isWithinStoreHours(dateTime: DateTime, workingHours: WorkingHours[]): boolean {
    const dayOfWeek = dateTime.weekdayLong?.toLowerCase()
    const storeHours = workingHours.find(h => h.day === dayOfWeek)
    
    if (!storeHours || !storeHours.isOpen) {
      return false
    }
    
    const timeString = dateTime.toFormat('HH:mm')
    return timeString >= storeHours.openTime && timeString <= storeHours.closeTime
  }

  private async getExistingAppointments(
    storeId: string,
    startTime: DateTime,
    endTime: DateTime,
    staffId?: string
  ): Promise<ZnAppointment[]> {
    let query = ZnAppointment.query()
      .where('storeId', storeId)
      .where('status', '!=', 'cancelled')
      .where((builder) => {
        builder
          .whereBetween('startTime', [startTime.toJSDate(), endTime.toJSDate()])
          .orWhereBetween('endTime', [startTime.toJSDate(), endTime.toJSDate()])
          .orWhere((subBuilder) => {
            subBuilder
              .where('startTime', '<=', startTime.toJSDate())
              .where('endTime', '>=', endTime.toJSDate())
          })
      })
    
    if (staffId) {
      query = query.where('staffId', staffId)
    }
    
    return await query.exec()
  }

  private async checkStaffAvailability(
    staffId: string,
    startTime: DateTime,
    endTime: DateTime
  ): Promise<boolean> {
    // This would integrate with staff scheduling system
    // For now, assume staff is available during store hours
    return true
  }

  private async findNearbyAvailableSlots(
    storeId: string,
    requestedTime: DateTime,
    duration: number,
    staffId?: string,
    searchRadius: number = 120 // minutes
  ): Promise<DateTime[]> {
    const suggestions: DateTime[] = []
    
    // Search before and after the requested time
    for (let offset = this.SLOT_INTERVAL; offset <= searchRadius; offset += this.SLOT_INTERVAL) {
      // Check earlier time
      const earlierTime = requestedTime.minus({ minutes: offset })
      const earlierAvailability = await this.checkSlotAvailability(
        storeId,
        earlierTime,
        duration,
        staffId
      )
      
      if (earlierAvailability.available) {
        suggestions.push(earlierTime)
      }
      
      // Check later time
      const laterTime = requestedTime.plus({ minutes: offset })
      const laterAvailability = await this.checkSlotAvailability(
        storeId,
        laterTime,
        duration,
        staffId
      )
      
      if (laterAvailability.available) {
        suggestions.push(laterTime)
      }
      
      // Stop if we have enough suggestions
      if (suggestions.length >= 5) break
    }
    
    return suggestions.sort((a, b) => {
      const aDiff = Math.abs(a.diff(requestedTime, 'minutes').minutes)
      const bDiff = Math.abs(b.diff(requestedTime, 'minutes').minutes)
      return aDiff - bDiff
    })
  }

  private async getStoreStaff(storeId: string, staffId?: string): Promise<ZnUser[]> {
    let query = ZnUser.query()
      .where('storeId', storeId)
      .where('isActive', true)
    
    if (staffId) {
      query = query.where('id', staffId)
    }
    
    return await query.exec()
  }

  private async calculateStaffAvailability(
    staffId: string,
    startDate: DateTime,
    endDate: DateTime
  ): Promise<StaffAvailability> {
    // This would integrate with staff scheduling system
    // For now, return basic availability
    return {
      staffId,
      staffName: 'Staff Member',
      availableSlots: [],
      busySlots: [],
      preferences: {
        preferredHours: { start: '09:00', end: '17:00' },
        specialties: [],
        maxConsecutiveHours: 8,
        breakDuration: 30,
      },
    }
  }

  private sortAppointmentsByPriority(appointments: SchedulingRequest[]): SchedulingRequest[] {
    return appointments.sort((a, b) => {
      // Prioritize by flexibility (strict first)
      const flexibilityOrder = { strict: 0, moderate: 1, flexible: 2 }
      return flexibilityOrder[a.flexibility] - flexibilityOrder[b.flexibility]
    })
  }

  private async findBestSlotInMatrix(
    matrix: AvailabilityMatrix,
    request: SchedulingRequest
  ): Promise<{ slot?: ScheduleSlot }> {
    // Simplified implementation
    const availableSlot = matrix.timeSlots.find(slot => slot.available)
    
    if (availableSlot) {
      const totalDuration = request.duration || 60
      
      return {
        slot: {
          startTime: availableSlot.time,
          endTime: availableSlot.time.plus({ minutes: totalDuration }),
          services: request.serviceIds,
          totalDuration,
          totalPrice: await this.calculateSlotPrice(request.serviceIds),
          confidence: 0.8,
          reasons: ['Best available slot'],
        },
      }
    }
    
    return {}
  }

  private markSlotAsUsed(matrix: AvailabilityMatrix, slot: ScheduleSlot): void {
    const startIndex = matrix.timeSlots.findIndex(
      s => s.time.equals(slot.startTime)
    )
    
    if (startIndex >= 0) {
      const slotsNeeded = Math.ceil(slot.totalDuration / this.SLOT_INTERVAL)
      
      for (let i = startIndex; i < startIndex + slotsNeeded && i < matrix.timeSlots.length; i++) {
        matrix.timeSlots[i].available = false
        matrix.timeSlots[i].conflicts.push('Slot reserved')
      }
    }
  }

  private calculateScheduleEfficiency(slots: ScheduleSlot[], matrix: AvailabilityMatrix): number {
    if (slots.length === 0) return 0
    
    const totalScheduledTime = slots.reduce((sum, slot) => sum + slot.totalDuration, 0)
    const totalAvailableTime = matrix.timeSlots.length * this.SLOT_INTERVAL
    
    return totalScheduledTime / totalAvailableTime
  }

  private analyzeCustomerTimePreferences(customerContext: any): string[] {
    // Analyze customer's booking history to determine preferred times
    // This would look at past appointments and identify patterns
    return ['morning', 'afternoon'] // Simplified
  }

  private adjustConfidenceForCustomerPreferences(
    slot: ScheduleSlot,
    preferredTimes: string[],
    customerContext: any
  ): number {
    let confidence = slot.confidence
    
    // Boost confidence for preferred times
    const hour = slot.startTime.hour
    
    if (preferredTimes.includes('morning') && hour >= 9 && hour < 12) {
      confidence += 0.1
    }
    
    if (preferredTimes.includes('afternoon') && hour >= 13 && hour < 17) {
      confidence += 0.1
    }
    
    return Math.min(1, confidence)
  }

  private async getSlotConflicts(
    storeId: string,
    time: DateTime,
    staffId?: string
  ): Promise<string[]> {
    const conflicts: string[] = []
    
    // Check for existing appointments
    const appointments = await this.getExistingAppointments(
      storeId,
      time,
      time.plus({ minutes: this.SLOT_INTERVAL }),
      staffId
    )
    
    if (appointments.length > 0) {
      conflicts.push('Existing appointment')
    }
    
    return conflicts
  }

  private calculateSlotScore(time: DateTime, conflicts: string[]): number {
    let score = 1.0
    
    // Reduce score for conflicts
    score -= conflicts.length * 0.2
    
    // Prefer certain hours
    const hour = time.hour
    if (hour >= 10 && hour <= 16) {
      score += 0.1 // Prefer business hours
    }
    
    return Math.max(0, Math.min(1, score))
  }

  private async calculateSlotPrice(serviceIds: string[]): Promise<number> {
    const services = await ZnStoreService.query().whereIn('id', serviceIds)
    return services.reduce((sum, service) => sum + (service.price || 0), 0)
  }

  private async getCustomerPreferenceScore(
    customerId: string,
    time: DateTime
  ): Promise<number> {
    // This would analyze customer's booking history
    // For now, return a neutral score
    return 0.5
  }

  private formatWorkingHours(workingHours: WorkingHours[]): string {
    const openDays = workingHours.filter(h => h.isOpen)
    if (openDays.length === 0) return 'Closed'
    
    return openDays
      .map(h => `${h.day}: ${h.openTime}-${h.closeTime}`)
      .join(', ')
  }
}