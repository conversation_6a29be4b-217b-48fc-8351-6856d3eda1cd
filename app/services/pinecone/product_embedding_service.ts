import {PineconeEmbeddingService} from "#services/pinecone/pinecone_embedding_service";
import ZnProduct from "#models/zn_product";
import env from "#start/env";
import {htmlToText} from "../../../services/commons.js";
import {RecordMetadata} from "@pinecone-database/pinecone";

export default class ProductEmbeddingService extends PineconeEmbeddingService<ZnProduct> {
  private readonly rerankModel = 'bge-reranker-v2-m3'

  constructor() {
    const indexName = env.get('PROD_EMBEDDING_INDEX_NAME') ?? 'DUMPSTER'
    const embeddingModel = 'text-embedding-3-large'
    const dimension = 3072

    super(indexName, embeddingModel, dimension)
  }

  protected getId(product: ZnProduct) {
    return product.id
  }

  protected buildText(product: ZnProduct) {
    const title = product.title ?? ''
    const vendor = product.vendor?.companyName ?? ''
    const type = product.productType?.name ?? ''
    const collection =
      product.collections
        ?.map((c) => c.title)
        .filter(Boolean)
        .join(', ') ?? ''
    const body = htmlToText(product.description ?? '').trim()

    return [
      `Title: ${title} by ${vendor}.`,
      type && `Category: ${type}.`,
      collection && `Collections: ${collection}.`,
      `Description: ${body}.`,
    ]
      .filter(Boolean)
      .join(' ')
  }

  protected buildMetadata(product: ZnProduct): RecordMetadata {
    return {
      title        : product.title,
      productuctType  : product.productType?.name ?? '',
      price        : Number(product.price) || 0,
      description  : this.buildText(product),
      inStock      : (product.variant?.inventoryQuantity ?? 0) > 0,
    }
  }

  async query(text: string, topK = 10, filter?: Record<string, unknown>) {
    const coarse = await super.query(text, topK * 3, filter)
    const docs   = coarse.map(c => ({ id: c.id, text: c.metadata?.description as string }))

    const rerank = await this.pinecone.inference.rerank(
      this.rerankModel,
      text,
      docs,
      { topN: topK, rankFields: ['text'], returnDocuments: false },
    )

    return rerank.data.map(r => coarse[r.index])
  }


}
