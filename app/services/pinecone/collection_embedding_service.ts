import type { RecordMetadata } from '@pinecone-database/pinecone'
import env from '#start/env'
import ZnCollection from "#models/zn_collection";
import {PineconeEmbeddingService} from "#services/pinecone/pinecone_embedding_service";


export default class CollectionEmbeddingService extends PineconeEmbeddingService<ZnCollection> {
  constructor() {
    const indexName = env.get('COLLECTION_EMBEDDING_INDEX_NAME') ?? 'DUMPSTER-COL'
    const embeddingModel = 'text-embedding-3-small'
    const dimension = 1536

    super(indexName, embeddingModel, dimension)
  }

  protected getId(collection: ZnCollection) {
    return collection.id
  }
  protected buildText(collection: ZnCollection) {
    return `Title: ${collection.title ?? ''} \n Description: ${collection.description ?? ''}\n`.trim()
  }
  protected buildMetadata(collection: ZnCollection): RecordMetadata {
    return {
      title: collection.title ?? '',
      description: this.buildText(collection)
    }
  }
}
