import Redis from '@adonisjs/redis/services/main'

/**
 * CacheService provides methods to manage Redis cache operations
 * including invalidation of cached responses from CacheResponse decorator
 */
export class CacheService {
  /**
   * Invalidate cache for a specific endpoint
   * @param method HTTP method (GET, POST, PUT, DELETE, etc.)
   * @param url URL path of the endpoint
   * @param queryParams Query parameters (optional)
   * @param authToken Authorization token (optional)
   */
  public static async invalidateCache(
    method: string,
    url: string,
    queryParams: Record<string, any> = {},
    authToken?: string
  ): Promise<void> {
    // Generate cache key for authenticated user
    if (authToken) {
      const userKey = authToken
      const cacheKey = `_${method}_${url}_${JSON.stringify(queryParams)}_user:${userKey}`
      await Redis.del(cacheKey)
    }

    // Also invalidate cache for anonymous users
    const anonymousCacheKey = `_${method}_${url}_${JSON.stringify(queryParams)}_user:anonymous`
    await Redis.del(anonymousCacheKey)

    // If we're not sure if the cache is per-user or not, also invalidate the 'all' users cache
    const allUsersCacheKey = `_${method}_${url}_${JSON.stringify(queryParams)}_user:all`
    await Redis.del(allUsersCacheKey)
  }

  /**
   * Invalidate cache for a specific resource by ID
   * @param resourceType Type of resource (posts, users, products, etc.)
   * @param resourceId ID of the resource
   * @param authToken Authorization token (optional)
   */
  public static async invalidateResourceCache(
    _resourceType: string,
    _resourceId: string,
    _authToken?: string
  ): Promise<void> {
    // Temporary use flushall
    await Redis.flushall()
  }
}

export default CacheService
