import ZnProduct from '#models/zn_product'
import ZnAIAssistant, { EAIAssistantRole } from '#models/zn_ai_assistant'
import OpenAI from 'openai'
import { IvsChatBotService } from '#services/aws/ivschat_bot_service'
import ProductEmbeddingService from '#services/pinecone/product_embedding_service'
import type { RecordMetadata } from '@pinecone-database/pinecone'
import ZnOrder from "#models/zn_order";
import CollectionEmbeddingService from "#services/pinecone/collection_embedding_service";

export interface ProductConstraints {
  price_lower?: number
  price_upper?: number
  inStockOnly?: number
}

interface ExtractedOrderFilters {
  order_numbers?: string[]                    // e.g. ["#1234","#5678"]
  order_status?: 'open' | 'archived' | 'canceled'
  financial_status?:                               // camel-cased DB column = financialStatus
    | 'authorized' | 'pending' | 'paid'
    | 'partially_paid' | 'partially_refunded'
    | 'refunded' | 'voided' | 'due' | 'expired' | 'unpaid'
  fulfillment_status?:                            // DB column = fulfillmentStatus
    | 'fulfilled' | 'unfulfilled' | 'partially_fulfilled'
    | 'scheduled' | 'on_hold' | 'request_declined'
  total_price?: { min?: number; max?: number }    // DB column = totalPrice
  fulfilled_at?: { from?: string; to?: string }   // ISO-8601 dates, DB column = createdAt (or fulfilledAt)
}

export interface ProductRecommendation {
  prodId: string
  metadata: RecordMetadata
}

export interface CollectionRecommendation {
  collectionId: string
  metadata: RecordMetadata
}


export default class ZurnoAssistantService {
  private openai
  private ivsChatBotService
  private productEmbeddingService
  private collectionEmbeddingService

  constructor() {
    this.openai = new OpenAI()
    this.ivsChatBotService = new IvsChatBotService()
    this.productEmbeddingService = new ProductEmbeddingService()
    this.collectionEmbeddingService = new CollectionEmbeddingService()
  }

  public buildOrderQuery(userId: string, filters: ExtractedOrderFilters) {
    const query = ZnOrder.query().where('userId', userId)
    if (filters.order_numbers?.length) query.whereIn('name', filters.order_numbers)
    if (filters.order_status) query.where('orderStatus', filters.order_status)
    if (filters.financial_status)  query.where('financialStatus', String(filters.financial_status))
    if (filters.fulfillment_status)  query.where('fulfillmentStatus', String(filters.fulfillment_status))
    if (filters.total_price?.min !== undefined) query.where('totalPrice', '>=', filters.total_price.min)
    if (filters.total_price?.max !== undefined) query.where('totalPrice', '<=', filters.total_price.max)
    if (filters.fulfilled_at?.from) query.where('createdAt', '>=', filters.fulfilled_at.from)
    if (filters.fulfilled_at?.to) query.where('createdAt', '<=', filters.fulfilled_at.to)
    return query
  }

  public async extractOrdersTag(content: string) {
    const orderFilterSystemPrompt = `
      You are OrderFilterExtractor, a function-calling assistant whose ONLY task is to turn a user’s free-form order-search request into the JSON arguments for the \\extract_order_filters\\ tool.

      ───────── Rules ─────────
      1. Always output exactly ONE tool call – no prose, no Markdown.
      2. Your output must be valid JSON that matches the tool schema.
      3. Normalise language to the enums below (taken directly from the Shopify UI).
         **Return only the keys that the user’s request actually implies.**

         • financial_status →
           "authorized" | "pending" | "paid" | "partially_paid" | "partially_refunded" | "refunded" | "voided" | "due" | "expired" | "unpaid"

         • fulfillment_status →
           "fulfilled" | "unfulfilled" | "partially_fulfilled" | "scheduled" | "on_hold" | "request_declined"

         • order_status →
           "open" | "archived" | "canceled"

      4. Date phrases → ISO-8601 (e.g. “today”, “yesterday”, “last week”…).
      5. Price phrases → \\total_price.min\\ / \\total_price.max\\
         (supports “over 50”, “<=20”, “between 10 and 30”…).
      6. Notes → any quoted words or “with note containing …” → \\note_keywords\\ array.
      7. **Order numbers** → any token matching /#?\\d{3,}/ → **order_numbers** array.
         Always **include** the leading “#”; store as strings ⇒ \`"order_numbers": ["#1234", "#5678"]\`.
      8. If nothing in the user request maps to this schema, return \\{}\\.
      9. Don’t hallucinate values the user never implied.

      ───────── Few-shot guidance ─────────
      _User says → you reply with JSON only_

      **Example 1**
      USER: “Show all refunded orders from Andy that haven’t been fulfilled.”
      ASSISTANT:
      {
        "financial_status": "refunded",
        "fulfillment_status": "unfulfilled",
        "order_status": "open"
      }

      **Example 2**
      USER: “Any partially-refunded orders over 50 USD between 1 May and 7 May 2025?”
      ASSISTANT:
      {
        "financial_status": "partially_refunded",
        "total_price": { "min": 50 },
        "currency": "USD",
        "fulfilled_at": { "from": "2025-05-01", "to": "2025-05-07" }
      }

      **Example 3**
      USER: “I need invoices for orders #1231 and #4521 that are still open.”
      ASSISTANT:
      {
        "order_numbers": ["#1231", "#4521"],
        "order_status": "open"
      }

      Output **only** the JSON arguments for the tool call – nothing else.
   `


    try {
      const response = await this.openai.chat.completions.create({
        model:  'gpt-4.1',
        temperature: 0.1,
        messages: [
          {
            role: 'system',
            content: orderFilterSystemPrompt
          },
          {role: 'user', content}
        ]
      })
      const receivedMessage = response.choices?.[0]?.message?.content || content
      return this.sanitizeAssistantText(receivedMessage)
    } catch (error) {
      console.log(error)
      return {}
    }

  }

  async AIParaphrase(content: string, language: string = 'en') {
    let paraphrasedMessage = content
    try {
      const response = await this.openai.chat.completions.create({
        model: 'gpt-3.5-turbo-0125',
        max_tokens: 60,
        temperature: 0.8,
        messages: [
          {
            role: 'system',
            content:
              `You are a friendly, upbeat copywriter for a beauty-supply brand. ` +
              `Rewrite the user's text to sound warm, engaging, and professional while preserving meaning. ` +
              `**Always reply in ${language}. Output only the rewritten text.**`,
          },
          { role: 'user', content },
        ],
      })
      paraphrasedMessage = response.choices?.[0]?.message?.content || content
    } catch (error) {
      console.log('AI paraphrase error:', error)
    }
    return paraphrasedMessage
  }

  async provideContextForAssistant(openaiThreadId: string, content: string) {
    await this.openai.beta.threads.messages.create(openaiThreadId, {
      role: 'assistant',
      content: content,
    })
  }

  public async selectAssistant(userMessage: string): Promise<EAIAssistantRole> {
    const orchestratorSystemPrompt = `
      You are the ORCHESTRATOR for Zurno’s multi-agent system.

      Your job:
      • Examine each incoming user message.
      • Pick exactly one of the following agents to handle the reply:
        – SHOPPING_ASSISTANT        (product discovery, how-to/use questions, compatibility, bulk quotes, stock checks)
        – CUSTOMER_SERVICE          (website issues, feedback about live chat, policy inquiries)
        – ORDER_ASSISTANT           (order status, tracking, delivery problems, returns, refunds)

      Routing rules:
      1. Ignore language. If the user writes in Vietnamese—or any language other than English—treat it exactly as if it were written in English.
      2. Base the choice only on the user’s intent, not on language or formatting.
      3. Always respond with a JSON object containing a single key "agent" whose value is the chosen agent name, for example:
         {"agent":"SHOPPING_ASSISTANT"}
         Do not include any other text, punctuation outside the JSON, or line breaks.

      Examples (illustrative; do not include in your response):
      User → "Tôi muốn mua 50 hộp gel đắp móng màu nude."
      Assistant → {"agent":"SHOPPING_ASSISTANT"}

      User → "How do I apply the rubber base coat correctly?"
      Assistant → {"agent":"SHOPPING_ASSISTANT"}

      User → "My parcel shows 'delivered' but I never got it."
      Assistant → {"agent":"ORDER_ASSISTANT"}

      User → "The live chat on your website hasn’t worked for three days!"
      Assistant → {"agent":"CUSTOMER_SERVICE"}

      CRITICAL: You must respond with EXACTLY one of these three agent names inside a JSON object and nothing else:
      * SHOPPING_ASSISTANT
      * CUSTOMER_SERVICE
      * ORDER_ASSISTANT
      `.trim()

    const orchestratorResponse = await this.openai.chat.completions.create({
      model: 'gpt-3.5-turbo',
      messages: [
        { role: 'system', content: orchestratorSystemPrompt },
        { role: 'user', content: userMessage },
      ],
      temperature : 0.1
    })
    const rawOrchestratorResponse = orchestratorResponse?.choices?.[0]?.message?.content ?? ''
    const orchestratorResponseParsed = this.sanitizeAssistantText(rawOrchestratorResponse)
    switch (orchestratorResponseParsed.agent) {
      case 'SHOPPING_ASSISTANT':
        return EAIAssistantRole.SHOPPING_ASSISTANT
      case 'ORDER_ASSISTANT':
        return EAIAssistantRole.ORDER_ASSISTANT
      default:
        return EAIAssistantRole.CUSTOMER_SERVICE
    }
  }

  private buildPineconeFilter(constraints: ProductConstraints): Record<string, any> | undefined {
    const filter: Record<string, any> = {}

    if (constraints.price_lower !== undefined || constraints.price_upper !== undefined) {
      filter.price = {}
      if (constraints.price_upper !== undefined) {
        filter.price.$lte = constraints.price_upper
      }
      if (constraints.price_lower !== undefined) {
        filter.price.$gte = constraints.price_lower
      }
    }

    if (constraints.inStockOnly === 1) filter.inStock = true

    return Object.keys(filter).length ? filter : undefined
  }

  private async evaluateRecommendation(userDescription: string, productDescription: string) {
    const evaluationPrompt = `
      You are a product‐matching evaluator.
      Given the user’s original request:
        "${userDescription}"
      And the recommended product’s description:
        "${productDescription}"
      Decide if the recommended product fits the user’s original request.
      Answer strictly “Yes” or “No” (no extra text).
     `
    const evaluation = await this.openai.chat.completions.create({
      model: 'gpt-4-turbo',
      messages: [
        { role: 'system', content: 'You are a evaluator.' },
        {
          role: 'user',
          content: evaluationPrompt.trim(),
        },
      ],
    })
    return evaluation.choices[0].message.content?.trim() === 'Yes'
  }

  public async getCollectionRecommendation(descriptions: string | string[] ) {
    let ids = new Set<string>();
    let collectionsRecommendation : CollectionRecommendation[] = []
    const recommendationPerDescription = Math.max(Math.ceil(5 / descriptions.length), 1);
    console.log(descriptions)
    for (const description of descriptions) {
      const potentialRecommendations = await this.collectionEmbeddingService.query(description, 3)

      let foundRecommendations = 0
      const collectionSuggestions = potentialRecommendations.map((match) => ({
        collectionId: match.id || '',
        metadata: (match.metadata ?? {}) as RecordMetadata,
        score: match.score,
      }))

      for (const collection of collectionSuggestions) {
        if (ids.has(collection.collectionId)) continue
        const verdict = true
        if (verdict) {
          collectionsRecommendation.push(collection)
          ids.add(collection.collectionId)
          foundRecommendations++
          if (foundRecommendations >= recommendationPerDescription) break
          if (collectionsRecommendation.length >= 5) break
        }
      }
    }
    return collectionsRecommendation

  }

  public async getProductRecommendation(
    descriptions: string | string[],
    constraints: ProductConstraints
  ): Promise<ProductRecommendation[]> {

    let ids = new Set<string>();
    let productRecommendations : ProductRecommendation[] = []
    const pineconeFilter = this.buildPineconeFilter(constraints)
    const recommendationPerDescription = Math.max(Math.ceil(5 / descriptions.length), 1);
    console.log('pineconeFilter', pineconeFilter);
    for (const description of descriptions) {
      const potentialRecommendations = await this.productEmbeddingService.query(
        description, 5,
        pineconeFilter
      )


      let foundRecommendations = 0
      const productSuggestions = potentialRecommendations.map((match) => ({
        prodId: match.id || '',
        metadata: (match.metadata ?? {}) as RecordMetadata,
        score: match.score,
      }))

      for (const product of productSuggestions) {
        if (ids.has(product.prodId)) continue
        const userDescription = description
        const productDescription = product.metadata!.description as string
        const verdict = await this.evaluateRecommendation(description, productDescription)
        console.log('User description: ', userDescription)
        console.log('Product description: ', productDescription)
        console.log(`Product evaluation for ${product.score}: ${verdict}`)
        if (verdict) {
          productRecommendations.push(product)
          ids.add(product.prodId)
          foundRecommendations++
          if (foundRecommendations >= recommendationPerDescription) break
          if (productRecommendations.length >= 5) break
        }
      }
    }
    return productRecommendations
  }

  public async getAIAnswer(assistant: ZnAIAssistant, openaiThreadId: string, content: string) {
    if (assistant) {
      await this.openai.beta.threads.messages.create(openaiThreadId, {
        role: 'user',
        content: content,
      })

      let result = await this.openai.beta.threads.runs.createAndPoll(openaiThreadId, {
        assistant_id: assistant.openaiAssistantId,
      })

      let updatedRun
      while (true) {
        updatedRun = await this.openai.beta.threads.runs.retrieve(openaiThreadId, result.id)
        if (updatedRun.status !== 'in_progress') break
        await new Promise((resolve) => setTimeout(resolve, 1000))
      }

      if (updatedRun.status === 'requires_action') {
        //@ts-ignore
        const toolCall = updatedRun?.required_action.submit_tool_outputs.tool_calls
        console.log('toolCall', toolCall)

        const tool_outputs = await Promise.all(
          toolCall?.map(async (call) => {
            const args = JSON.parse(call.function.arguments)
            const dataResponse = await this.handleChatbotFunctions(call.function.name, args)

            return {
              tool_call_id: call.id,
              output: JSON.stringify(dataResponse),
            }
          })
        )
        await this.openai.beta.threads.runs.submitToolOutputs(openaiThreadId, result.id, {
          tool_outputs,
        })
      }

      await this.ivsChatBotService.waitForLastRunToComplete(openaiThreadId)
      // if (result.status !== 'completed') return

      // Fetch the latest assistant message only
      const messages = await this.openai.beta.threads.messages.list(openaiThreadId, { limit: 1 })
      const latest = messages.data[0]

      const contentBlock = latest.content.find((c) => c.type === 'text')

      if (contentBlock && contentBlock.type === 'text') {
        const text = contentBlock.text.value.trim()
        return this.sanitizeAssistantText(text)
      }
    }
    return null
  }
  public sanitizeAssistantText(raw: string) {
    let cleaned = raw.trim()

    // Remove ```json or ``` or citations like
    cleaned = cleaned
      .replace(/^```json/, '')
      .replace(/^```/, '')
      .replace(/```$/, '')
      .replace(/\[\d+:\d+†[^\]]+\]/g, '')
      .replace(/【[^】]+】/g, '')
      .trim()

    let parsed

    try {
      parsed = JSON.parse(cleaned)
    } catch (error) {
      // If not valid JSON, wrap as fallback structure
      parsed = {
        text: cleaned,
        productIds: [],
        collectionIds: [],
        postIds: [],
        skus: [],
        questions: [],
      }
    }

    // Sanitize array fields to ensure valid JSON
    parsed.productIds = (parsed.productIds || []).map((id: string) =>
      id.trim().replace('product_', '')
    )
    parsed.collectionIds = (parsed.collectionIds || []).map((id: string) =>
      id.trim().replace('collection_', '')
    )
    parsed.postIds = (parsed.postIds || []).map((id: string) => id.trim().replace('post_', ''))
    parsed.skus = (parsed.skus || []).map((sku: string) => sku.trim())
    parsed.questions = (parsed.questions || []).map((q: string) => q.trim())

    return parsed
  }

  async handleChatbotFunctions(
    func: string,
    args: {
      name: any
    }
  ) {
    let output
    switch (func) {
      case 'get_product_info':
        const search = args.name.trim().toLowerCase()

        const products = await ZnProduct.query()
          .where((queryBuilder) => {
            queryBuilder
              .whereRaw('LOWER(title) LIKE ?', [`%${search}%`])
              .orWhereRaw('LOWER(tags) LIKE ?', [`%${search}%`])
              .orWhereHas('variants', (variantQuery) => {
                variantQuery.whereRaw('LOWER(sku) LIKE ?', [`%${search}%`])
              })
          })
          .where('status', 'active')
          .orderByRaw('RAND()')
          .limit(5)

        output = {
          products: products,
          productIds: products?.map((prod) => prod.id) || [],
        }
        break
    }
    return output
  }
}
