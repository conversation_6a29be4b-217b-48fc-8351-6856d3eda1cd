import ZnUser from "#models/zn_user";
import Zn<PERSON>end<PERSON> from "#models/zn_vendor";
import { md5 } from 'js-md5'

export default class VendorService {
  async getAllVendors(page: number, limit: number, search: string | undefined) {
    const query = ZnVendor.query();

    if (search) {
      query.whereILike('companyName', `%${search}%`)
        .orWhereILike('brandName', `%${search}%`)
        .orWhereILike('website', `%${search}%`)
        .orWhereILike('contactName', `%${search}%`)
        .orWhereILike('phone', `%${search}%`)
        .orWhereILike('email', `%${search}%`);
    }

    query.orderBy('companyName', 'asc');

    return await query.paginate(page, limit);
  }

  async getVendorById(vendorId: string) {
    return await ZnVendor.query()
      .where('id', vendorId)
      .preload('businessLicenseDocuments')
      .preload('users')
      .firstOrFail();
  }

  async getVendorByUserId(userId: string) {
    return await ZnVendor.query()
      .whereHas('users', (query) => {
        query.where('id', userId);
      })
      .firstOrFail();
  }

  async createVendor(newVendor: any) {
    // Verify whether the vendor existed
    let existingVendor = await ZnVendor.query()
      .where('companyName', newVendor.companyName)
      .first();
    if (existingVendor) {
      return {
        success: false,
        messagge: 'This company name is already registered'
      }
    }

    existingVendor = await ZnVendor.query()
      .where('ein', newVendor.ein)
      .first();
    if (existingVendor) {
      return {
        success: false,
        messagge: 'This EIN is already registered'
      }
    }

    existingVendor = await ZnVendor.query()
      .where('email', newVendor.email)
      .first();
    if (existingVendor) {
      return {
        success: false,
        messagge: 'This email is already used by an existing vendor'
      }
    }

    // Create vendor
    const createdVendor = await ZnVendor.create({
      companyName: newVendor.companyName,
      brandName: newVendor.brandName,
      website: newVendor.website,
      contactName: newVendor.contactName,
      phone: newVendor.phone,
      email: newVendor.email,
      address1: newVendor.address1,
      address2: newVendor.address2,
      city: newVendor.city,
      state: newVendor.state,
      country: newVendor.country,
      zipCode: newVendor.zipCode,
      ein: newVendor.ein,
    });
    await createdVendor.related('businessLicenseDocuments').sync(newVendor.businessLicenseDocumentIds);

    // Create user
    await this.createUserForVendor(createdVendor.id, newVendor.contactName, newVendor.phone, newVendor.email);

    return {
      success: true,
      vendor: createdVendor
    };
  }

  async update(vendorId: string, payload: any) {
    const updatingVendor = await ZnVendor.findOrFail(vendorId);

    if (payload.companyName) updatingVendor.companyName = payload.companyName;
    if (payload.brandName) updatingVendor.brandName = payload.brandName;
    if (payload.website) updatingVendor.website = payload.website;
    if (payload.contactName) updatingVendor.contactName = payload.contactName;
    if (payload.phone) updatingVendor.phone = payload.phone;
    if (payload.email) updatingVendor.email = payload.email;
    if (payload.address1) updatingVendor.address1 = payload.address1;
    if (payload.address2) updatingVendor.address2 = payload.address2;
    if (payload.city) updatingVendor.city = payload.city;
    if (payload.state) updatingVendor.state = payload.state;
    if (payload.country) updatingVendor.country = payload.country;
    if (payload.zipCode) updatingVendor.zipCode = payload.zipCode;
    if (payload.ein) updatingVendor.ein = payload.ein;
    await updatingVendor.save();

    if (payload.businessLicenseDocumentIds) {
      await updatingVendor.related('businessLicenseDocuments').sync(payload.businessLicenseDocumentIds);
      await updatingVendor.load('businessLicenseDocuments');
    }

    // Create new user if necessary
    if (payload.email && payload.email !== updatingVendor.email) {
      await this.createUserForVendor(updatingVendor.id, payload.contactName, payload.phone, payload.email);
    }

    return updatingVendor;
  }

  async delete(vendorId: string) {
    const vendor = await ZnVendor.find(vendorId);
    if (!vendor) return;
    await vendor.softDelete();
  }

  private async createUserForVendor(vendorId: string, contactName: string, phone: string, email: string) {
    const nameParts = this.splitContactName(contactName);
    const timestamp = Date.now().toString();

    const user = await ZnUser.firstOrCreate({
      email
    }, {
      firstName: nameParts.firstName,
      lastName: nameParts.lastName,
      phone,
      email,
      password: md5(timestamp)
    });

    user.vendorId = vendorId;
    return await user.save();
  }

  private splitContactName(contactName: string): { firstName: string; lastName: string; } {
    // Handle empty or whitespace-only strings
    if (!contactName || !contactName.trim()) {
      return { firstName: '', lastName: '' };
    }

    const trimmed = contactName.trim();
    const parts = trimmed.split(/\s+/); // Split on one or more whitespace characters

    if (parts.length === 1) {
      // Only one name provided
      return { firstName: parts[0], lastName: '' };
    }

    const firstName = parts[0];
    const lastName = parts.slice(1).join(' ');

    return { firstName, lastName };
  }
}