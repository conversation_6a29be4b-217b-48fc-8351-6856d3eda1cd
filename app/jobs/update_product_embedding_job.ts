import {BaseEmbeddingJob} from "#jobs/base_embedding_job";
import ZnProduct from "#models/zn_product";
import ProductEmbeddingService from "#services/pinecone/product_embedding_service";

export default class UpdateProductEmbeddingJob extends BaseEmbeddingJob<ZnProduct> {
  protected embeddingService = new ProductEmbeddingService()

  static get $$filepath() { return import.meta.url }

  protected lookup(ids: string[]) {
    return ZnProduct.query()
      .whereIn('id', ids)
      .preload('collections')
      .preload('vendor')
      .preload('productType')
      .preload('variant')
      .preload('reviewsSummary')
  }
}
