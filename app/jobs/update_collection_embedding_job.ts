import {BaseEmbeddingJob} from "#jobs/base_embedding_job";
import ZnCollection from "#models/zn_collection";
import CollectionEmbeddingService from "#services/pinecone/collection_embedding_service";

export default class UpdateCollectionEmbeddingJob extends BaseEmbeddingJob<ZnCollection> {
  protected  embeddingService = new CollectionEmbeddingService()

  static get $$filepath() { return import.meta.url }

  protected lookup(ids: string[]) {
    return ZnCollection.query().whereIn('id', ids).preload('categories')
  }
}
