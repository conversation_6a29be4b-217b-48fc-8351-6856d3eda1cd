import type { HttpContext } from '@adonisjs/core/http'
import ZnCall from '#models/zn_call'
import OpenAI from 'openai'
import ZnCallMessage from '#models/zn_call_message'
import ZnStore from '#models/zn_store'
import twilio from 'twilio'

export default class AiReceptionistsController {
  private openai: OpenAI

  constructor() {
    this.openai = new OpenAI()
  }

  /**
   * Generate TwiML for saying text and listening for response
   */
  private sayAndListen(message: string, timeout: number = 5): string {
    const twiml = new twilio.twiml.VoiceResponse()
    twiml.say(message)
    twiml.gather({
      input: ['speech'],
      timeout: timeout,
      action: '/api/app/ai-receptionists/process-speech',
      method: 'POST',
      speechTimeout: 'auto',
    })
    return twiml.toString()
  }

  /**
   * Generate TwiML for just saying text
   */
  private say(message: string): string {
    const twiml = new twilio.twiml.VoiceResponse()
    twiml.say(message)
    return twiml.toString()
  }

  /**
   * Translate voice recording to text using OpenAI Whisper
   */
  private async translateVoiceToText(recordingUrl: string): Promise<string> {
    try {
      // Download the audio file
      const response = await fetch(recordingUrl)
      const audioBuffer = await response.arrayBuffer()

      // Convert to File object for OpenAI
      const audioFile = new File([new Uint8Array(audioBuffer)], 'audio.wav', { type: 'audio/wav' })

      // Use OpenAI Whisper for transcription
      const transcription = await this.openai.audio.transcriptions.create({
        file: audioFile,
        model: 'whisper-1',
      })

      return transcription.text || 'Could not understand audio'
    } catch (error) {
      console.error('Voice to text error:', error)
      return 'Could not process audio'
    }
  }

  /**
   * Handle goodbye/bye responses
   */
  private handleBye(): string {
    return this.say('Thank you for calling! Have a great day!')
  }

  /**
   * Handle transfer to human agent
   */
  private redialToAgent(): string {
    return this.say('Let me transfer you to one of our team members. Please hold on.')
  }

  /**
   * Handle pause/wait responses
   */
  private handlePause(message: any): string {
    const twiml = new twilio.twiml.VoiceResponse()
    twiml.pause({ length: 3 })
    twiml.redirect(`/api/app/ai-receptionists/process-pause?messageId=${message.id}`)
    return twiml.toString()
  }

  /**
   * Enhanced conversation processing using AI services
   */
  private async processWithAI(
    userMessage: string,
    _storeId: string,
    _sessionId: string
  ): Promise<string> {
    try {
      // For now, use a simplified AI processing approach
      // This can be enhanced later with full dependency injection

      // Basic intent recognition
      if (
        userMessage.toLowerCase().includes('appointment') ||
        userMessage.toLowerCase().includes('book')
      ) {
        return "I'd be happy to help you book an appointment! What service are you interested in?"
      }

      if (
        userMessage.toLowerCase().includes('service') ||
        userMessage.toLowerCase().includes('what do you offer')
      ) {
        return "We offer a variety of services. Could you tell me what type of service you're looking for?"
      }

      if (
        userMessage.toLowerCase().includes('hours') ||
        userMessage.toLowerCase().includes('open')
      ) {
        return 'Our hours vary by day. What day were you thinking of visiting?'
      }

      if (
        userMessage.toLowerCase().includes('price') ||
        userMessage.toLowerCase().includes('cost')
      ) {
        return 'Pricing depends on the specific service. Which service would you like to know about?'
      }

      // Default response
      return "I understand you're looking for assistance. Could you please tell me more specifically how I can help you today?"
    } catch (error) {
      console.error('AI processing error:', error)
      return "I'm sorry, I'm having trouble processing your request. Could you please try again?"
    }
  }
  async welcome({ request, response }: HttpContext) {
    const callSid = request.input('CallSid')
    const fromNumber = request.input('From') // E.g., "+15551234567"
    const toNumber = request.input('To') // E.g., "+15551234567"

    console.log('Incoming call from:', fromNumber, 'to:', toNumber, 'callSid:', callSid)

    try {
      // Find store by phone number
      const store = await ZnStore.query()
        .where('phone', toNumber)
        .orWhere('phone', toNumber.replace(/\D/g, '')) // Remove non-digits
        .first()

      if (!store) {
        console.error('Store not found for phone number:', toNumber)
        const errorMessage = 'Sorry, we could not identify the store. Please contact support.'
        const errorResponse = this.sayAndListen(errorMessage)
        response.type('text/xml')
        response.send(errorResponse)
        return
      }

      console.log('Found store:', store.name, 'ID:', store.id)

      // Search for previous call thread
      let threadId
      const previousCall = await ZnCall.query()
        .where({
          fromNumber,
          toNumber,
        })
        .orderBy('createdAt', 'desc')
        .first()

      if (previousCall) {
        threadId = previousCall.threadId
        console.log('Using existing thread:', threadId)
      } else {
        const thread = await this.openai.beta.threads.create({})
        threadId = thread.id
        console.log('Created new thread:', threadId)
      }

      // Create call record
      const callRecord = await ZnCall.create({
        fromNumber: fromNumber,
        toNumber: toNumber,
        clientCallId: callSid,
        threadId,
        storeId: store.id, // Associate with store
      })

      console.log('Created call record:', callRecord.id)

      // Generate personalized welcome message
      const welcomeMessage = `Hi! I'm ${store.name}'s AI receptionist. I can help you book appointments, check availability, or answer questions about our services. How can I assist you today?`
      const welcome = this.sayAndListen(welcomeMessage)

      response.type('text/xml')
      response.send(welcome)
    } catch (error) {
      console.error('Welcome handler error:', error)
      const errorMessage =
        'Sorry, we are experiencing technical difficulties. Please try again later.'
      const errorResponse = this.sayAndListen(errorMessage)
      response.type('text/xml')
      response.send(errorResponse)
    }
  }

  async processFlow({ request, response }: HttpContext) {
    const callSid = request.input('CallSid')
    const room = await ZnCall.query().where('clientCallId', callSid).first()
    let answer = 'Sorry, we are not able to talk'

    if (room) {
      try {
        const recordingUrl = request.input('RecordingUrl') + '.wav' // Twilio gives .mp3, use .wav for Whisper
        console.log('Processing call:', callSid, 'Recording URL:', recordingUrl)

        const userSaid = await this.translateVoiceToText(recordingUrl)
        console.log('Caller said:', userSaid)

        // Create message record
        const userMessage = await ZnCallMessage.create({
          callId: room?.id,
          message: userSaid,
        })

        // Check for immediate actions first
        if (userSaid.toLowerCase().includes('bye') || userSaid.toLowerCase().includes('goodbye')) {
          answer = this.handleBye()
        } else if (
          userSaid.toLowerCase().includes('agent') ||
          userSaid.toLowerCase().includes('human')
        ) {
          answer = this.redialToAgent()
        } else {
          // Use simplified AI processing
          const aiResponseText = await this.processWithAI(userSaid, room.storeId, room.id)

          // Store AI response
          userMessage.reply = aiResponseText
          await userMessage.save()

          // Generate TwiML response
          answer = this.sayAndListen(aiResponseText)
        }
      } catch (error) {
        console.error('Process flow error:', error)
        answer = this.sayAndListen(
          "I'm sorry, I'm having trouble processing your request. Could you please try again?"
        )
      }
    }
    response.type('text/xml')
    response.send(answer)
  }

  async processReceptionist({ params, response }: HttpContext) {
    const messageId = params.id
    console.log(messageId)
    //Getting AI answer
    const aiResponse = messageId ? await ZnCallMessage.query().where('id', messageId).first() : null
    let answer = 'Sorry'
    if (aiResponse) {
      if (aiResponse.reply) {
        //If ready
        answer = this.sayAndListen(aiResponse.reply)
      } else {
        //Wait more 5s
        answer = this.handlePause(aiResponse)
      }
    }

    response.type('text/xml')
    response.send(answer)
  }
}
