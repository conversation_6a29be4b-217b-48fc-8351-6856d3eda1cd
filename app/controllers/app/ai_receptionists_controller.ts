import type { HttpContext } from '@adonisjs/core/http'
import { ZurnoAIReceptionistService } from '#services/zurno_ai_receptionist_service'
import ZnCall from '#models/zn_call'
import OpenAI from 'openai'
import ZnCallMessage from '#models/zn_call_message'
import ZnStore from '#models/zn_store'

export default class AiReceptionistsController {
  private zurnoAIReceptionistService
  private openai
  constructor() {
    this.zurnoAIReceptionistService = new ZurnoAIReceptionistService()
    this.openai = new OpenAI()
  }
  async welcome({ request, response }: HttpContext) {
    const callSid = request.input('CallSid')
    const fromNumber = request.input('From') // E.g., "+15551234567"
    const toNumber = request.input('To') // E.g., "+15551234567"

    console.log('Incoming call from:', fromNumber, 'to:', toNumber, 'callSid:', callSid)

    try {
      // Find store by phone number
      const store = await ZnStore.query()
        .where('phone', toNumber)
        .orWhere('phone', toNumber.replace(/\D/g, '')) // Remove non-digits
        .first()

      if (!store) {
        console.error('Store not found for phone number:', toNumber)
        const errorMessage = 'Sorry, we could not identify the store. Please contact support.'
        const errorResponse = this.zurnoAIReceptionistService.sayAndListen(errorMessage)
        response.type('text/xml')
        response.send(errorResponse)
        return
      }

      console.log('Found store:', store.name, 'ID:', store.id)

      // Search for previous call thread
      let threadId
      const previousCall = await ZnCall.query()
        .where({
          fromNumber,
          toNumber,
        })
        .orderBy('createdAt', 'desc')
        .first()

      if (previousCall) {
        threadId = previousCall.threadId
        console.log('Using existing thread:', threadId)
      } else {
        const thread = await this.openai.beta.threads.create()
        threadId = thread.id
        console.log('Created new thread:', threadId)
      }

      // Create call record
      const callRecord = await ZnCall.create({
        fromNumber: fromNumber,
        toNumber: toNumber,
        clientCallId: callSid,
        threadId,
        storeId: store.id, // Associate with store
      })

      console.log('Created call record:', callRecord.id)

      // Generate personalized welcome message
      const welcomeMessage = `Hi! I'm ${store.name}'s AI receptionist. I can help you book appointments, check availability, or answer questions about our services. How can I assist you today?`
      const welcome = this.zurnoAIReceptionistService.sayAndListen(welcomeMessage)

      response.type('text/xml')
      response.send(welcome)
    } catch (error) {
      console.error('Welcome handler error:', error)
      const errorMessage =
        'Sorry, we are experiencing technical difficulties. Please try again later.'
      const errorResponse = this.zurnoAIReceptionistService.sayAndListen(errorMessage)
      response.type('text/xml')
      response.send(errorResponse)
    }
  }

  async processFlow({ request, response }: HttpContext) {
    const callSid = request.input('CallSid')
    const room = await ZnCall.query().where('clientCallId', callSid).first()
    let answer = 'Sorry, we are not able to talk'

    if (room) {
      try {
        const recordingUrl = request.input('RecordingUrl') + '.wav' // Twilio gives .mp3, use .wav for Whisper
        console.log('Processing call:', callSid, 'Recording URL:', recordingUrl)

        const userSaid = await this.zurnoAIReceptionistService.translateVoiceToText(recordingUrl)
        console.log('Caller said:', userSaid)

        // Create message record
        const userMessage = await ZnCallMessage.create({
          callId: room?.id,
          message: userSaid,
        })

        // Check for immediate actions first
        if (userSaid.toLowerCase().includes('bye') || userSaid.toLowerCase().includes('goodbye')) {
          answer = this.zurnoAIReceptionistService.handleBye()
        } else if (
          userSaid.toLowerCase().includes('agent') ||
          userSaid.toLowerCase().includes('human')
        ) {
          answer = this.zurnoAIReceptionistService.redialToAgent()
        } else {
          // Use enhanced AI processing
          const aiResponse = await this.zurnoAIReceptionistService.handleReception(
            room,
            userMessage,
            userSaid
          )

          // Handle different response types
          if (aiResponse && aiResponse.nextAction === 'transfer_to_human') {
            answer = this.zurnoAIReceptionistService.redialToAgent()
          } else if (aiResponse && aiResponse.nextAction === 'end_conversation') {
            answer = this.zurnoAIReceptionistService.handleBye()
          } else if (userMessage.reply) {
            // AI has processed and stored response
            answer = this.zurnoAIReceptionistService.sayAndListen(userMessage.reply)
          } else {
            // Fallback to pause and process
            answer = this.zurnoAIReceptionistService.handlePause(userMessage)
          }
        }
      } catch (error) {
        console.error('Process flow error:', error)
        answer = this.zurnoAIReceptionistService.sayAndListen(
          "I'm sorry, I'm having trouble processing your request. Could you please try again?"
        )
      }
    }
    response.type('text/xml')
    response.send(answer)
  }

  async processReceptionist({ params, response }: HttpContext) {
    const messageId = params.id
    console.log(messageId)
    //Getting AI answer
    const aiResponse = messageId ? await ZnCallMessage.query().where('id', messageId).first() : null
    let answer = 'Sorry'
    if (aiResponse) {
      if (aiResponse.reply) {
        //If ready
        await this.zurnoAIReceptionistService.sayAndListen(aiResponse.reply)
        answer = this.zurnoAIReceptionistService.handleLoop()
      } else {
        //Wait more 5s
        answer = this.zurnoAIReceptionistService.handlePause(aiResponse)
      }
    }

    response.type('text/xml')
    response.send(answer)
  }
}
