import { belongsTo, column, computed, hasMany } from '@adonisjs/lucid/orm'
import type { BelongsTo, HasMany } from '@adonisjs/lucid/types/relations';
import ZnAffiliate from './zn_affiliate.js';
import AppModel from './app_model.js';
import { EApprovalStatus } from '#constants/approval_status';
import ZnAffiliateRefCode from './zn_affiliate_refcode.js';
import ZnOrder from './zn_order.js';
import ZnAffiliateCommissionDetail from './zn_affiliate_commission_detail.js';

export default class ZnAffiliateCommission extends AppModel {
    @column({ columnName: 'affiliateId' })
    declare affiliateId: string

    @column({ columnName: 'orderId' })
    declare orderId: string

    @column({
        columnName: 'commissionAmount',
        consume: (value: string) => parseFloat(value)
    })
    declare commissionAmount: number

    @column({ columnName: 'status' })
    declare status: string | EApprovalStatus.PENDING

    @column({ columnName: 'rejectReason' })
    declare rejectReason: string | null

    @column({ columnName: 'refCodeId' })
    declare refCodeId: string

    @column({
        columnName: 'adjustedAmount',
        consume: (value: string) => parseFloat(value)
    })
    declare adjustedAmount: number

    @column({ columnName: 'adjustedReason' })
    declare adjustedReason: string

    @computed()
    get finalAmount() {
        if (this.adjustedAmount !== null && this.adjustedAmount > 0)
            return this.adjustedAmount
        return this.commissionAmount
    }

    @belongsTo(() => ZnAffiliate, {
        foreignKey: 'affiliateId'
    })
    declare affiliate: BelongsTo<typeof ZnAffiliate>

    @belongsTo(() => ZnOrder, {
        foreignKey: 'orderId'
    })
    declare order: BelongsTo<typeof ZnOrder>

    @belongsTo(() => ZnAffiliateRefCode, {
        foreignKey: 'refCodeId'
    })
    declare refCode: BelongsTo<typeof ZnAffiliateRefCode>

    @hasMany(() => ZnAffiliateCommissionDetail, {
        foreignKey: 'commissionId'
    })
    declare commissionDetails: HasMany<typeof ZnAffiliateCommissionDetail>
}