import AppMail from '#mails/app_mail';

export default class CommissionNotification extends AppMail {
  constructor(
    private userFirstName: string,
    private isApproved: boolean,
    private commissionId: string,
    private orderTime: string,
    private audienceName: string,
    private orderTotal: string,
    private commissionAmount: string,
    private refCode: string,
    private rejectReason: string | null,
    private userEmail: string,
  ) {
    super()
  }

  /**
   * The "prepare" method is called automatically when
   * the email is sent or queued.
   */
  prepare() {
    this.message
      .subject(this.isApproved ? "You've Earned a New Commission!" : 'Update: Commission Request Rejected')
      .htmlView(this.isApproved ? 'mails/affiliation/commission_approved' : 'mails/affiliation/commission_rejected', {
        serverDomain: this.baseUrl,
        userFirstName: this.userFirstName,
        commissionId: this.commissionId,
        orderTime: this.orderTime,
        audienceName: this.audienceName,
        orderTotal: this.orderTotal,
        commissionAmount: this.commissionAmount,
        refCode: this.refCode,
        rejectReason: this.rejectReason,
      })
      .to(this.userEmail)
  }
}
