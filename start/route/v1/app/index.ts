import router from '@adonisjs/core/services/router'

import authRoutes from './auth.js'
import cartSectionRoutes from './cart-section.js'
import cartRoutes from './cart.js'
import checkoutRoutes from './checkout.js'
import customerRoutes from './customer.js'
import giftRoutes from './gift.js'
import appNailSystemsRoutes from './nail-system.js'
import notificationRoutes from './notification.js'
import productReviewRoutes from './product-review.js'
import profileRoutes from './profile.js'
import reportRoutes from './report.js'
import reviewRoutes from './review.js'
import rewardRoutes from './reward.js'
import settingRoutes from './setting.js'
import shopRoutes from './shop.js'
import storeServiceRoutes from './store-service.js'
import appointmentRoutes from './appointment.js'

export default function appRoutes() {
  router
    .group(() => {
      authRoutes()
      notificationRoutes()
      profileRoutes()
      settingRoutes()
      appNailSystemsRoutes()
      reviewRoutes()
      reportRoutes()
      productReviewRoutes()
      giftRoutes()
      checkoutRoutes()
      cartRoutes()
      shopRoutes()
      rewardRoutes()
      cartSectionRoutes()
      storeServiceRoutes()
      customerRoutes()
      appointmentRoutes()
    })
    .prefix('app')
}
