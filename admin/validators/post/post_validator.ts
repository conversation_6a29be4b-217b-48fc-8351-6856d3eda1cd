import vine from '@vinejs/vine'
import { TRANSLATIONS_ENUM } from '../../../app/constants/app.js'
import { EPostSource, EPostType } from '#models/zn_post'

export const createPostValidator = vine.compile(
  vine.object({
    title: vine.string().optional().requiredWhen('source', '!=', EPostSource.REEL),
    price: vine.string().optional(),
    description: vine.string().optional(),
    categoryIds: vine
      .array(
        vine
          .string()
          .uuid()
          .exists(async (query, field) => {
            const postCategory = await query.from('zn_post_categories').where('id', field).first()
            return postCategory
          })
      )
      .optional(),
    thumbnailId: vine
      .string()
      .uuid()
      .exists(async (query, field) => {
        const media = await query.from('zn_medias').where('id', field).first()
        return media
      })
      .optional(),
    thumbnailUrl: vine.string().activeUrl().optional(),

    mediaIds: vine
      .array(
        vine
          .string()
          .uuid()
          .exists(async (query, field) => {
            const media = await query.from('zn_medias').where('id', field).first()
            return media
          })
      )
      .optional(),

    youtubeUrl: vine.string().activeUrl().optional(),

    isDraft: vine.enum([0, 1]).optional(),
    isFavourite: vine.enum([0, 1]).optional(),
    isUnlist: vine.enum([0, 1]).optional(),

    url: vine.string().url().optional(),

    countryId: vine.string().optional(),
    stateId: vine.string().optional(),
    cityId: vine.string().optional(),

    country: vine.string().optional(),
    state: vine.string().optional(),
    city: vine.string().optional(),

    address: vine.string().optional(),
    address2: vine.string().optional(),
    latitude: vine.string().optional(),
    longitude: vine.string().optional(),
    zipcode: vine.string().optional(),

    originLocale: vine.enum(TRANSLATIONS_ENUM).optional(),
    translations: vine
      .array(
        vine.object({
          id: vine.string().uuid().optional(),
          locale: vine.enum(TRANSLATIONS_ENUM),
          field: vine.string(),
          value: vine.string(),
        })
      )
      .optional(),

    userId: vine
      .string()
      .uuid()
      .exists(async (query, field) => {
        const user = await query.from('zn_users').where('id', field).first()
        return user
      })
      .optional(),
    contactName: vine.string().optional(),
    phone: vine.string().optional(),
    email: vine.string().email().optional(),

    storeId: vine
      .string()
      .uuid()
      .exists(async (query, field) => {
        const store = await query.from('zn_stores').where('id', field).first()
        return store
      })
      .optional(),

    vietId: vine.number().optional(),

    expired: vine.enum([0, 1]).optional(),

    createdByAdminId: vine
      .string()
      .uuid()
      .exists(async (query, field) => {
        const admin = await query.from('zn_admins').where('id', field).first()
        return admin
      })
      .optional(),

    type: vine.enum(EPostType).optional(),

    timelines: vine
      .array(
        vine.object({
          id: vine.string().uuid().optional(),
          variantId: vine.string().uuid(),
          start: vine.number(),
          end: vine.number(),
        })
      )
      .optional(),

    scheduledAt: vine
      .date({ formats: { utc: true } })
      .afterOrEqual('today')
      .optional(),
  })
)
