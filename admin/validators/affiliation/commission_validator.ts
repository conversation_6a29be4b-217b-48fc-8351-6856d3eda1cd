import { EApprovalStatus } from '#constants/approval_status'
import vine from '@vinejs/vine'

export const commissionAdjustmentValidator = vine.compile(
  vine.object({
    adjustedAmount: vine.number(),
    adjustedReason: vine.string()
  })
)

export const commissionStatusValidator = vine.compile(
  vine.object({
    status: vine.enum(EApprovalStatus),
    rejectReason: vine.string().optional()
  })
)