import { ACTION, RESOURCE } from "#constants/authorization";
import VendorService from "#services/vendors/vendor_service";
import { HttpContext } from "@adonisjs/core/http";

export default class AdminVendorController {
  private vendorService: VendorService;

  constructor() {
    this.vendorService = new VendorService();
  }

  async index({ bouncer, request, response }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.READ, RESOURCE.VENDOR);

      const { page = 1, limit = 10, search } = request.all();
      const vendors = await this.vendorService.getAllVendors(page, limit, search);

      return response.ok(vendors);
    } catch (error) {
      console.error(error);
      return response.badRequest(error);
    }
  }

  async show({ bouncer, params, response }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.READ, RESOURCE.VENDOR);
      const { id } = params;
      if (!id) {
        return response.badRequest({ message: "Vendor ID is required" });
      }

      const vendor = await this.vendorService.getVendorById(id);
      return response.ok(vendor);
    } catch (error) {
      console.error(error);
      return response.badRequest(error);
    }
  }

  async store({ bouncer, response }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.CREATE, RESOURCE.VENDOR);
      return response.ok('store');
    } catch (error) {
      console.error(error);
      return response.badRequest(error);
    }
  }

  async update({ bouncer, params, request, response }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.UPDATE, RESOURCE.VENDOR);
      const id = params.id;
      if (!id) {
        return response.badRequest({ message: "Vendor ID is required" });
      }
      const data = request.all();
      const updatedVendor = await this.vendorService.update(id, data);
      return response.ok(updatedVendor);
    } catch (error) {
      console.error(error);
      return response.badRequest(error);
    }
  }

  async destroy({ bouncer, params, response }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.DELETE, RESOURCE.VENDOR);
      const { id } = params;
      if (!id) {
        return response.badRequest({ message: "Vendor ID is required" });
      }
      await this.vendorService.delete(id);
      return response.ok({ message: "Vendor deleted successfully" });
    } catch (error) {
      console.error(error);
      return response.badRequest(error);
    }
  }
}