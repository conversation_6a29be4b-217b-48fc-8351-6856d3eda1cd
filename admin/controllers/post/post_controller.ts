import ZnCity from '#models/zn_city'
import ZnCountry from '#models/zn_country'
import ZnPost, { EPostSource } from '#models/zn_post'
import ZnPostCategory from '#models/zn_post_category'
import ZnState from '#models/zn_state'
import ZnStore from '#models/zn_store'
// import ZnUser from '#models/zn_user'
import JwtService from '#services/jwt_service'
import { HttpContext } from '@adonisjs/core/http'
import { ACTION, RESOURCE } from '../../../app/constants/authorization.js'
import { createPostValidator } from '../../validators/post/post_validator.js'
import { uploadMediasFromLinks } from '../../../services/media/index.js'
import ZnPostTranslation from '#models/zn_post_translation'
import moment from 'moment'
import { PostService } from "#services/post_service";
import { TRACKING_ACTION } from '#constants/tracking'
import { MEDIA_TYPE } from '#constants/media'
import { DateTime } from 'luxon'

export default class AdminPostController {
  private postService: PostService

  constructor() {
    this.postService = new PostService()
  }
  /**
   * @index
   * @tag Admin Post
   * @summary Read all posts
   * @paramQuery page - Page Number (default 1) - @type(number)
   * @paramQuery limit - Page Limit (default 10) - @type(number)
   * @responseBody 200 - <ZnPost[]>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null,"price":"200","isDraft":"0","isFavourite":"1","expired":"0","categories":["ZnCategory"]).with(thumbnail, medias, user).paginated() - Read all posts descriptively
   * @responseBody 401 - Unauthorized access - Unauthorized
   */
  /**
   * Display a list of resource
   */
  async index({ bouncer, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.READ, RESOURCE.POST)

    try {
      const {
        page = 1,
        limit = 10,
        search,
        // categoryIds,
        // storeIds,
        latitude,
        longitude,
        // isDraft,
        // isFavourite,
        // priceFrom,
        // priceTo,
        filter,
        lazyFilter,
        sort,
        sortBy,
        miles,
      } = request.qs()

      const query = ZnPost.query()
        .preload('store', (storeQuery) => {
          storeQuery.preload('logo')
        })
        .preload('categories', (categoryQuery) => {
          categoryQuery.preload('thumbnail')
        })
        .preload('medias')
        .preload('thumbnail')
        .preload('user')
        .preload('createdByAdmin')
        .whereNull('deletedAt')
        // .whereNull('source')
        .where((queryBuilder) => {
          queryBuilder.whereNot('source', EPostSource.ZURNO).orWhereNull('source')
        })

      if (search) {
        const postIds = await this.postService.searchByText(search)
        const formattedIds = postIds.map((id) => `'${id}'`).join(', ')

        if (postIds.length > 0) {
          query.whereIn('id', postIds).orderByRaw(`FIELD(id, ${formattedIds})`)
        } else {
          const translations = await ZnPostTranslation.query().whereRaw(
            'LOWER(value) LIKE LOWER(?)',
            [`%${search}%`]
          )

          query.where(async (queryBuilder) => {
            queryBuilder
              .whereRaw('LOWER(title) LIKE LOWER(?)', [`%${search}%`])
              .orWhereRaw('LOWER(description) LIKE LOWER(?)', [`%${search}%`])
              .orWhereIn(
                'id',
                translations.map((translation) => translation.postId)
              )
          })
        }
      }

      // if (isDraft) {
      //   query.where('isDraft', isDraft)
      // }

      // if (isFavourite) {
      //   query.where('isFavourite', isFavourite)
      // }

      // if (categoryIds && categoryIds.length > 0) {
      //   const categoryIdsArray = Array.isArray(categoryIds) ? categoryIds : [categoryIds]

      //   query.whereHas('categories', (categoryQuery) => {
      //     categoryQuery.whereIn('zn_post_categories.id', categoryIdsArray)
      //   })
      // }

      // if (storeIds && storeIds.length > 0) {
      //   const storeIdsArray = Array.isArray(storeIds) ? storeIds : [storeIds]

      //   query.whereHas('store', (storeQuery) => {
      //     storeQuery.whereIn('zn_stores.id', storeIdsArray)
      //   })
      // }

      if (filter) {
        filter.map((fil: string) => {
          if (fil.startsWith('fromDate')) {
            const fromDate = moment(fil.split('=')[1]).format('yyyy-MM-DD')
            query.whereRaw(`createdAt >= '${fromDate}'`)
          } else if (fil.startsWith('toDate')) {
            const toDate = moment(fil.split('=')[1]).add(1, 'day').format('yyyy-MM-DD')
            query.whereRaw(`createdAt <= '${toDate}'`)
          }
        })
      }

      if (lazyFilter) {
        const categoryIds: string[] = []
        const userIds: string[] = []
        const adminIds: string[] = []
        const storeIds: string[] = []

        lazyFilter.map((fil: string) => {
          // query.where(
          //   fil.split("=")[0],
          //   fil.split("=")[1]
          // )
          // const categoryIds = []

          if (fil.startsWith('categoryId')) {
            categoryIds.push(fil.split('=')[1])
            // query.whereHas('categories', (categoryQuery) => {
            //   categoryQuery.where('zn_post_categories.id', fil.split("=")[1])
            // })
          } else if (fil.startsWith('userId')) {
            userIds.push(fil.split('=')[1])
            // query.whereHas('user', (userQuery) => {
            //   userQuery.where('zn_users.id', fil.split("=")[1])
            // })
          } else if (fil.startsWith('createdByAdminId')) {
            adminIds.push(fil.split('=')[1])
            // query.whereHas('createdByAdmin', (adminQuery) => {
            //   adminQuery.where('zn_admins.id', fil.split("=")[1])
            // })
          } else if (fil.startsWith('storeId')) {
            storeIds.push(fil.split('=')[1])
          }
        })

        if (categoryIds.length > 0) {
          query.whereHas('categories', (categoryQuery) => {
            categoryQuery.whereIn('zn_post_categories.id', categoryIds)
          })
        }
        if (userIds.length > 0) {
          query.whereIn('userId', userIds)
        }
        if (adminIds.length > 0) {
          query.whereIn('createdByAdminId', adminIds)
        }
        if (storeIds.length > 0) {
          query.whereIn('storeId', storeIds)
        }
      }

      if (sort || sortBy) {
        if (sortBy === 'distance' && latitude && longitude) {
          const lat = Number.parseFloat(latitude)
          const lon = Number.parseFloat(longitude)

          query.where((queryBuilder) => {
            queryBuilder
              .orWhereRaw(
                `
              (6371 * acos(cos(radians(?)) * cos(radians(latitude)) * cos(radians(longitude) - radians(?)) + sin(radians(?)) * sin(radians(latitude)))) <= 100
              `,
                [lat, lon, lat]
              )
              .orWhereHas('store', (storeQuery) => {
                storeQuery.whereRaw(
                  `
                (6371 * acos(cos(radians(?)) * cos(radians(latitude)) * cos(radians(longitude) - radians(?)) + sin(radians(?)) * sin(radians(latitude)))) <= 100
                `,
                  [lat, lon, lat]
                )
              })
          })
        }

        if (sortBy === 'distance' && latitude && longitude && miles) {
          const lat = Number.parseFloat(latitude)
          const lon = Number.parseFloat(longitude)
          const maxDistance = Number.parseFloat(miles)

          query.where((queryBuilder) => {
            queryBuilder
              .whereRaw(
                `
                (3959 * acos(cos(radians(?)) * cos(radians(latitude)) * cos(radians(longitude) - radians(?)) + sin(radians(?)) * sin(radians(latitude)))) <= ?
                `,
                [lat, lon, lat, maxDistance]
              )
              .orWhereHas('store', (storeQuery) => {
                storeQuery.whereRaw(
                  `
                  (3959 * acos(cos(radians(?)) * cos(radians(latitude)) * cos(radians(longitude) - radians(?)) + sin(radians(?)) * sin(radians(latitude)))) <= ?
                  `,
                  [lat, lon, lat, maxDistance]
                )
              })
          })
        }

        if (sortBy === 'popular') {
          query.orderBy('isFavourite', 'desc')
        }

        if (sortBy === 'newest') {
          query.orderBy('createdAt', 'desc')
        }

        if (sort && sort.length > 0) {
          query.orderBy(sort[0], sort[1])
        }
      } else {
        query.orderBy('createdAt', 'desc')
      }

      // if (priceFrom || priceTo) {
      //   const parsedPriceFrom = priceFrom ? Number(Number.parseFloat(priceFrom).toFixed(2)) : null
      //   const parsedPriceTo = priceTo ? Number(Number.parseFloat(priceTo).toFixed(2)) : null
      //   query.where((priceQuery) => {
      //     if (parsedPriceFrom !== null) {
      //       priceQuery.where('price', '>=', parsedPriceFrom)
      //     }
      //     if (parsedPriceTo !== null) {
      //       priceQuery.where('price', '<=', parsedPriceTo)
      //     }
      //   })
      // }

      const result = await query.paginate(page, limit)

      return response.ok(result)
    } catch (error) {
      console.log(error)

      return response.badRequest(error)
    }
  }

  // /**
  //  * @filter
  //  * @tag Admin Post
  //  * @summary Filter posts
  //  * @requestBody {}
  //  * @responseBody 200 - <ZnPost[]>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null,"price":"200","isDraft":"0","isFavourite":"1","expired":"0","categories":["ZnCategory"]).with(thumbnail, medias, user).paginated() - Read all posts descriptively
  //  * @responseBody 401 - Unauthorized access - Unauthorized
  //  */
  // async filter({ bouncer, request, response }: HttpContext) {
  //   await bouncer.authorize('allow', ACTION.READ, RESOURCE.POST)

  //   const {
  //     page = 1,
  //     limit = 10,
  //   } = request.all()

  //   try {
  //     const query = ZnPost.query()
  //       .preload('store', (storeQuery) => {
  //         storeQuery.preload('logo')
  //       })
  //       .preload('categories', (categoryQuery) => {
  //         categoryQuery.preload('thumbnail')
  //       })
  //       .preload('medias')
  //       .preload('thumbnail')
  //       .preload('user')
  //       .preload('createdByAdmin')
  //       .whereNull('deletedAt')
  //       .whereNull('source')

  //     const result = await query.paginate(page, limit)

  //     return response.ok(result)

  //   } catch (error) {
  //     return response.badRequest(error)
  //   }
  // }

  /**
   * @create
   * @tag Admin Post
   * @summary Return info for creation
   * @responseBody 200 - {"info":{"postCategories":"<ZnPostCategory>","stores":"<ZnStore>"}} - Return info for creation
   * @responseBody 401 - Unauthorized access - Unauthorized
   */
  /**
   * Display form to create a new record
   */
  async create({ bouncer, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.CREATE, RESOURCE.POST)

    try {
      // const users = await ZnUser.query()
      //   .select('id', 'firstName', 'lastName', 'phone', 'email')
      //   .whereNull('deletedAt')

      const postCategories = await ZnPostCategory.query()
        .select('id', 'name')
        .whereNull('deletedAt')
        .whereNull('parentId')

      // const stores = await ZnStore.query().preload('user').whereNull('deletedAt')

      // const countries = await ZnCountry.query().select('id', 'name').whereNull('deletedAt')

      // const states = await ZnState.query().select('id', 'name', 'countryId').whereNull('deletedAt')

      // const cities = await ZnCity.query().select('id', 'name', 'stateId').whereNull('deletedAt')

      return response.ok({
        info: {
          // users: users,
          postCategories: postCategories,
          // stores: stores,
          // countries: countries,
          // states: states,
          // cities: cities,
        },
      })
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @store
   * @tag Admin Post
   * @summary Create action
   * @requestBody <ZnPost>
   * @responseBody 201 - <ZnPost>.append("id":""") - Create action descriptively
   * @responseBody 400 - {"message":"Validation failed","errors":[{"message":"The title field must be defined","rule":"required","field":"title"}]} - Bad Request
   * @responseBody 401 - Unauthorized access - Unauthorized
   */
  /**
   * Handle form submission for the create action
   */
  async store({ bouncer, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.CREATE, RESOURCE.POST)

    // @ts-ignore
    const data = request.all()

    const payload = await createPostValidator.validate(data)

    try {
      let thumbnail
      if (payload.thumbnailUrl) {
        const thumbnails = await uploadMediasFromLinks([payload.thumbnailUrl], MEDIA_TYPE.IMAGE)
        if (thumbnails.length > 0) {
          thumbnail = thumbnails[0]
        }
      }

      const created = await ZnPost.create({
        userId: payload.userId,
        title: payload.title,
        description: payload.description ?? undefined,
        price: payload.price ? Number(Number.parseFloat(payload.price).toFixed(2)) : undefined,
        isDraft: payload.isDraft !== undefined ? payload.isDraft : (true as any),
        isFavourite: payload.isFavourite !== undefined ? payload.isFavourite : (false as any),
        isUnlist: payload.isUnlist !== undefined ? payload.isUnlist : (false as any),
        url: payload.url ?? undefined,
        latitude: payload.latitude ?? undefined,
        longitude: payload.longitude ?? undefined,
        address: payload.address ?? undefined,
        address2: payload.address2 ?? undefined,
        phone: payload.phone ?? undefined,
        zipcode: payload.zipcode ?? undefined,
        contactName: payload.contactName ?? undefined,
        email: payload.email ?? undefined,
        vietId: payload.vietId ?? undefined,
        expired: payload.expired !== undefined ? payload.expired : (false as any),
        thumbnailId: thumbnail?.id ?? payload.thumbnailId ?? undefined,
        storeId: payload.storeId ?? null,

        countryId: payload.countryId ?? undefined,
        stateId: payload.stateId ?? undefined,
        cityId: payload.cityId ?? undefined,

        youtubeUrl: payload.youtubeUrl ?? undefined,
        createdByAdminId: payload.createdByAdminId,
        type: payload.type || null,
        scheduledAt: payload.scheduledAt ? DateTime.fromJSDate(payload.scheduledAt) : null
      })

      // If scheduledAt is set, default to draft
      if (created.scheduledAt) { created.isDraft = true }

      const store = await ZnStore.find(payload.storeId || '')

      created.latitude = created.latitude || store?.latitude || null
      created.longitude = created.longitude || store?.longitude || null

      if (payload.mediaIds && payload.mediaIds.length > 0) {
        await created.related('medias').sync(payload.mediaIds)
      }

      if (payload.categoryIds && payload.categoryIds.length > 0) {
        await created.related('categories').sync(payload.categoryIds)
      }

      if (payload.country) {
        const country = await ZnCountry.query()
          .whereRaw(`LOWER(name) LIKE LOWER(?)`, [`${payload.country}`])
          .first()
        created.countryId = country ? country.id : ''
      }

      if (payload.state) {
        const state = await ZnState.query()
          .whereRaw(`LOWER(name) LIKE LOWER(?)`, [`${payload.state}`])
          .first()
        created.stateId = state ? state.id : ''
      }

      if (payload.city) {
        const city = await ZnCity.query()
          .whereRaw(`LOWER(name) LIKE LOWER(?)`, [`${payload.city}`])
          .first()
        created.cityId = city ? city.id : ''
      }

      if (payload.timelines) {
        await this.postService.updateOrCreateVideoTimelines(created.id, payload.timelines)
      }

      await created.save()

      return response.created(created)
    } catch (error) {
      console.log(error)

      return response.internalServerError({
        message: 'Something went wrong',
      })
    }
  }

  /**
   * @show
   * @tag Admin Post
   * @summary Read a post
   * @paramPath id - ID of Post - @type(string) @required
   * @responseBody 200 - <ZnPost>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null,"mine":false) - Read a post descriptively
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Post not found"} - Not Found
   */
  /**
   * Show individual record
   */
  async show({ bouncer, params, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.READ, RESOURCE.POST)

    try {
      let requestUserId

      const authToken = request.header('Authorization') as string

      JwtService.decodeToken(authToken, (decodedToken: Partial<{ userId: string }>) => {
        if (decodedToken) {
          requestUserId = decodedToken.userId
        }
      })

      const postId = params.id

      const post = await ZnPost.query()
        .preload('store', (storeQuery) => {
          storeQuery.preload('logo')
        })
        .preload('categories', (categoryQuery) => {
          categoryQuery.preload('thumbnail')
        })
        .preload('medias')
        .preload('thumbnail')
        .preload('user')
        .preload('country')
        .preload('state')
        .preload('city')
        .preload('translations')
        .where('id', postId)
        // .whereNull('source')
        .where((queryBuilder) => {
          queryBuilder.whereNot('source', EPostSource.ZURNO).orWhereNull('source')
        })
        .first()



      return response.ok({
        ...post?.serialize(),
        mine: post?.userId === requestUserId,
      })
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @edit
   * @tag Admin Post
   * @summary Return info for updating
   * @responseBody 200 - {} - Return info for updating
   * @responseBody 401 - Unauthorized access - Unauthorized
   */
  /**
   * Edit individual record
   */
  async edit({ bouncer, params, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.UPDATE, RESOURCE.POST)

    try {
      let requestUserId
      const postId = params.id

      const authToken = request.header('Authorization') as string

      JwtService.decodeToken(authToken, (decodedToken: Partial<{ userId: string }>) => {
        if (decodedToken) {
          requestUserId = decodedToken.userId
        }
      })

      const post = await ZnPost.query()
        .preload('store', (storeQuery) => {
          storeQuery.preload('logo')
        })
        .preload('categories', (categoryQuery) => {
          categoryQuery.preload('thumbnail')
        })
        .preload('medias')
        .preload('thumbnail')
        .preload('user')
        .preload('translations')
        .preload('timelines', (timelineQuery) => {
          timelineQuery.preload('variant', (variantQuery) => {
            variantQuery.preload('image')
              .preload('product', (productQuery) => {
                productQuery.preload('image')
              })
          })
        })
        .where('id', postId)
        .where((queryBuilder) => {
          queryBuilder.whereNot('source', EPostSource.ZURNO).orWhereNull('source')
        })
        .first()

      if (!post) {
        return response.notFound({ message: 'Post not found' })
      }

      const interactions = await this.postService.getInteractions(post.id, [
        TRACKING_ACTION.VIEW_POST,
        TRACKING_ACTION.ADD_WISHLIST,
        TRACKING_ACTION.SHARE,
        TRACKING_ACTION.CLICK_CALL_ON_POST,
      ])

      // added to cart variants
      const addedToCart = await this.postService.getAddedToCartVariants(post.id)

      return response.ok({
        data: {
          ...post?.serialize(),
          mine: post?.userId === requestUserId,
          interactions,
          addedToCart,
        },
      })
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @update
   * @tag Admin Post
   * @summary Update a post
   * @description Update a post descriptively
   * @paramPath id - ID of Post - @type(string) @required
   * @requestBody <ZnPost>
   * @responseBody 200 - <ZnPost>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null) - OK
   * @responseBody 400 - {"message":"Validation failed","errors":[{"message":"The title field must be defined","rule":"required","field":"title"}]} - Bad Request
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Post not found"} - Not Found
   */
  /**
   * Handle form submission for the edit action
   */
  async update({ bouncer, params, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.UPDATE, RESOURCE.POST)

    const postId = params.id

    const post = await ZnPost.query()
      .where('id', postId)
      // .whereNull('source')
      .where((queryBuilder) => {
        queryBuilder.whereNot('source', EPostSource.ZURNO).orWhereNull('source')
      })
      .first()

    if (!post) {
      return response.notFound({ message: 'Post not found' })
    }

    const data = request.all()

    const payload = await createPostValidator.validate(data)

    try {
      post.title = payload.title
      post.description = payload.description || post.description
      post.price = payload.price ? Number(Number.parseFloat(payload.price).toFixed(2)) : post.price

      post.isDraft =
        payload.isDraft !== undefined && payload.isDraft !== null
          ? payload.isDraft
          : (post.isDraft as any)
      post.isFavourite =
        payload.isFavourite !== undefined && payload.isFavourite !== null
          ? payload.isFavourite
          : (post.isFavourite as any)
      post.isUnlist =
        payload.isUnlist !== undefined && payload.isUnlist !== null
          ? payload.isUnlist
          : (post.isUnlist as any)
      post.expired =
        payload.expired !== undefined && payload.expired !== null
          ? payload.expired
          : (post.expired as any)

      post.type = payload.type || null
      // If scheduledAt is set, default to draft
      if (post.scheduledAt) { post.isDraft = true }

      post.countryId = payload.countryId || post.countryId
      if (payload.country) {
        const country = await ZnCountry.query()
          .whereRaw(`LOWER(name) LIKE LOWER(?)`, [`${payload.country}`])
          .first()
        // post.countryId = country ? country.id : post.countryId
        post.countryId = country ? country.id : ''
      }

      post.stateId = payload.stateId || post.stateId
      if (payload.state) {
        const state = await ZnState.query()
          .whereRaw(`LOWER(name) LIKE LOWER(?)`, [`${payload.state}`])
          .first()
        // post.stateId = state ? state.id : post.stateId
        post.stateId = state ? state.id : ''
      }

      post.cityId = payload.cityId || post.cityId
      if (payload.city) {
        const city = await ZnCity.query()
          .whereRaw(`LOWER(name) LIKE LOWER(?)`, [`${payload.city}`])
          .first()
        // post.cityId = city ? city.id : post.cityId
        post.cityId = city ? city.id : ''
      }

      // post.countryId = payload.countryId || post.countryId
      // post.stateId = payload.stateId || post.stateId
      // post.cityId = payload.cityId || post.cityId

      post.address = payload.address || post.address
      post.address2 = payload.address2 || post.address2
      // post.latitude = payload.latitude || post.latitude
      // post.longitude = payload.longitude || post.longitude
      post.zipcode = payload.zipcode || post.zipcode

      post.userId = payload.userId || null
      // post.userId = payload.userId || post.userId
      post.contactName = payload.contactName || post.contactName
      post.phone = payload.phone || post.phone
      post.email = payload.email || post.email

      post.url = payload.url || post.url
      post.vietId = payload.vietId || post.vietId

      post.storeId = payload.storeId || null
      // post.storeId = payload.storeId || post.storeId

      const store = await ZnStore.find(payload.storeId || '')
      post.latitude = payload.latitude || post.latitude || store?.latitude || null
      post.longitude = payload.longitude || post.longitude || store?.longitude || null

      let thumbnail
      if (payload.thumbnailUrl) {
        const thumbnails = await uploadMediasFromLinks([payload.thumbnailUrl], MEDIA_TYPE.IMAGE)
        if (thumbnails.length > 0) {
          thumbnail = thumbnails[0]
        }
      }

      post.thumbnailId = thumbnail?.id || payload.thumbnailId || null

      if (Array.isArray(payload.mediaIds)) {
        await post.related('medias').sync(payload.mediaIds)
      }

      if (Array.isArray(payload.categoryIds)) {
        await post.related('categories').sync(payload.categoryIds)
      }

      post.originLocale = payload.originLocale || null
      if (payload.translations) {
        for (const translation of payload.translations) {
          const translate = await ZnPostTranslation.find(translation.id || '')
          if (translate) {
            translate.value = translation.value
            await translate.save()
          }
          // else {
          //   await ZnPostTranslation.create({
          //     postId: post.id,
          //     locale: translation.locale,
          //     field: translation.field,
          //     value: translation.value,
          //   })
          // }
        }
      }

      post.youtubeUrl = payload.youtubeUrl || post.youtubeUrl

      if (payload.timelines) {
        await this.postService.updateOrCreateVideoTimelines(post.id, payload.timelines)
      }

      const updated = await post.save()

      return response.ok(updated)
    } catch (error) {
      return response.internalServerError({
        message: 'Something went wrong',
      })
    }
  }

  /**
   * @destroy
   * @tag Admin Post
   * @summary Soft-delete a post
   * @description Soft-delete a post descriptively
   * @paramPath id - ID of Post - @type(string) @required
   * @responseBody 200 - {"message":"Post soft-deleted successfully"} - OK
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Post not found"} - Not Found
   */
  /**
   * Delete record
   */
  async destroy({ auth, bouncer, params, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.DELETE, RESOURCE.POST, { children: true })

    const postId = params.id

    const post = await ZnPost.query()
      .where('id', postId)
      // .whereNull('source')
      .where((queryBuilder) => {
        queryBuilder.whereNot('source', EPostSource.ZURNO).orWhereNull('source')
      })
      .first()

    if (!post) {
      return response.notFound({ message: 'Post not found' })
    }

    // @ts-ignore
    if (post.createdByAdminId != auth.getUserOrFail()?.id) {
      await bouncer.authorize('deny', ACTION.DELETE_SELF, RESOURCE.POST)
    }

    await post.softDelete()

    return response.ok({ message: 'Post soft-deleted successfully' })
  }

  /**
   * @deleteLocaleTranslation
   * @tag Admin Post
   * @summary Delete a locale translation
   * @description Delete a locale translation descriptively
   * @paramPath id - ID of Post - @type(string) @required
   * @requestBody {"postId": "", "local": "vi"}
   * @responseBody 200 - {"message":"Post translations deleted successfully"} - OK
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Post not found"} - Not Found
   */
  async deleteLocaleTranslation({ bouncer, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.ACCESS_TRANSLATION, RESOURCE.POST)

    const postId = request.all().postId

    const post = await ZnPost.query()
      .where('id', postId)
      // .whereNull('source')
      .where((queryBuilder) => {
        queryBuilder.whereNot('source', EPostSource.ZURNO).orWhereNull('source')
      })
      .first()

    if (!post) {
      return response.notFound({ message: 'Post not found' })
    }

    const translations = await ZnPostTranslation.query()
      .where('postId', postId)
      .where('locale', request.all().locale)

    translations.map(async (translation) => await translation.delete())

    return response.ok({ message: 'Post translations deleted successfully' })
  }

  /**
   * @generateThumbnail
   * @tag Admin Post
   * @summary Generate thumbnail
   * @description Generate thumbnail for video post
   * @paramPath id - ID of Post - @type(string) @required
   * @responseBody 200 - {"message":"Thumbnail generated"} - OK
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Post not found"} - Not Found
   */
  async generateThumbnail({ bouncer, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.UPDATE, RESOURCE.POST)

    try {
      const postId = request.all().postId

      const post = await ZnPost.find(postId)

      if (!post) {
        return response.notFound({ message: 'Post not found' })
      }

      const thumbnail = await this.postService.generateThumbnailSync({ postId, overwrite: true })

      return response.ok(thumbnail)
      
    } catch (error){
      console.log(error);
      return response.internalServerError({
        message:"Something went wrong!",
        error
      })
    }
  }
}
