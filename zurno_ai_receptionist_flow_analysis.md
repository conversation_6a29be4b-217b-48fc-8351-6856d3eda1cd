# Zurno AI Receptionist Flow Analysis

## Overview

The Zurno AI Receptionist system is a voice-based AI assistant that handles incoming phone calls using Twilio integration, OpenAI Whisper for speech-to-text, and OpenAI GPT for natural language processing. The system manages call sessions, processes voice input, and provides intelligent responses.

## Core Components

### 1. Models

#### ZnCall Model

```typescript
// app/models/zn_call.ts
class ZnCall {
  clientCallId: string // Twilio Call SID
  fromNumber: string // Caller's phone number
  toNumber: string // Store's phone number
  userId: string // Associated user ID (nullable)
  storeId: string // Associated store ID (nullable)
  assistantId: string // AI Assistant ID (nullable)
  threadId: string // OpenAI Thread ID for conversation context
}
```

#### ZnCallMessage Model

```typescript
// app/models/zn_call_message.ts
class ZnCallMessage {
  callId: string // Reference to ZnCall
  userId: string // User who sent the message (nullable)
  message: string // Transcribed user speech
  reply: string // AI-generated response
}
```

### 2. Services

#### ZurnoAIReceptionistService

**Location:** `app/services/zurno_ai_receptionist_service.ts`

**Key Methods:**

- `sayAndListen(message)`: Generates Twilio TwiML for speech synthesis and recording
- `translateVoiceToText(recordingUrl)`: Downloads and transcribes audio using OpenAI Whisper
- `handleReception(room, message, userSaid)`: Processes user input through AI assistant
- `handleMakeAppointment()`: Handles appointment booking requests
- `redialToAgent()`: Transfers call to human agent
- `handlePause(userMessage)`: Implements waiting mechanism for AI processing

### 3. Controllers

#### AiReceptionistsController

**Location:** `app/controllers/app/ai_receptionists_controller.ts`

**Endpoints:**

- `POST /v1/receptions/` - Welcome endpoint (call initiation)
- `POST /v1/receptions/process-flow` - Main conversation flow
- `POST /v1/receptions/process-reception/:id` - AI response processing

## Detailed Flow Analysis

### 1. Call Initiation Flow (`welcome` method)

```mermaid
sequenceDiagram
    participant Twilio
    participant Controller
    participant ZnCall
    participant OpenAI
    participant Service

    Twilio->>Controller: POST /receptions/ (CallSid, From, To)
    Controller->>ZnCall: Query previous call by fromNumber/toNumber
    alt Previous call exists
        ZnCall-->>Controller: Return existing threadId
    else No previous call
        Controller->>OpenAI: Create new thread
        OpenAI-->>Controller: Return threadId
    end
    Controller->>ZnCall: Create new call record
    Controller->>Service: sayAndListen("Welcome message")
    Service-->>Controller: Return TwiML response
    Controller->>Twilio: Send TwiML (voice + record)
```

**Key Features:**

- **Session Management**: Maintains conversation context using OpenAI threads
- **Call Tracking**: Creates ZnCall record for each incoming call
- **Continuity**: Reuses existing thread for repeat callers
- **Welcome Message**: "Hi, I am Zurno AI Receptionist. How can I help you?"

### 2. Voice Processing Flow (`processFlow` method)

```mermaid
sequenceDiagram
    participant Twilio
    participant Controller
    participant Service
    participant OpenAI
    participant ZnCallMessage

    Twilio->>Controller: POST /process-flow (CallSid, RecordingUrl)
    Controller->>Service: translateVoiceToText(recordingUrl)
    Service->>Service: waitForTwilioAudio() - Download with retry
    Service->>OpenAI: Whisper transcription
    OpenAI-->>Service: Return transcribed text
    Service-->>Controller: Return user speech
    Controller->>ZnCallMessage: Create message record

    alt Contains "appointment"
        Controller->>Service: handleMakeAppointment()
    else Contains "bye"
        Controller->>Service: handleBye()
    else Contains "agent"
        Controller->>Service: redialToAgent()
    else General query
        Controller->>Service: handleReception() + handlePause()
    end

    Service-->>Controller: Return TwiML response
    Controller->>Twilio: Send TwiML
```

**Key Features:**

- **Speech-to-Text**: Uses OpenAI Whisper for accurate transcription
- **Intent Recognition**: Simple keyword-based routing
- **Message Persistence**: Stores all user messages in database
- **Robust Audio Handling**: Retry mechanism for Twilio audio download

### 3. AI Response Processing Flow (`processReceptionist` method)

```mermaid
sequenceDiagram
    participant Twilio
    participant Controller
    participant ZnCallMessage
    participant Service

    Twilio->>Controller: POST /process-reception/:id
    Controller->>ZnCallMessage: Query message by ID
    alt AI reply ready
        ZnCallMessage-->>Controller: Return message with reply
        Controller->>Service: sayAndListen(reply)
        Service-->>Controller: Return TwiML with loop redirect
    else AI still processing
        Controller->>Service: handlePause()
        Service-->>Controller: Return TwiML with 5s pause + retry
    end
    Controller->>Twilio: Send TwiML
```

**Key Features:**

- **Asynchronous Processing**: Handles AI response delays gracefully
- **Polling Mechanism**: Retries every 5 seconds until AI response ready
- **Conversation Loop**: Redirects back to main flow after response

### 4. AI Integration (`handleReception` method)

```mermaid
sequenceDiagram
    participant Service
    participant ZnAIAssistant
    participant ZurnoAssistantService
    participant OpenAI
    participant ZnCallMessage

    Service->>ZnAIAssistant: Find CUSTOMER_SERVICE assistant
    Service->>ZurnoAssistantService: getAIAnswer(assistant, threadId, userSaid)
    ZurnoAssistantService->>OpenAI: Process through assistant
    OpenAI-->>ZurnoAssistantService: Return AI response
    ZurnoAssistantService-->>Service: Return parsed response
    Service->>ZnCallMessage: Update message.reply
    ZnCallMessage-->>Service: Save to database
```

## Current Implementation Gaps

### 1. Appointment Booking Integration

**Current State:** Basic keyword detection for "appointment"
**Missing:**

- Integration with appointment booking system
- Natural language date/time parsing
- Service selection through voice
- Customer identification and creation
- Availability checking
- Booking confirmation

### 2. Store/Customer Context

**Current State:** Stores fromNumber/toNumber but doesn't use them
**Missing:**

- Store identification by phone number
- Customer lookup by phone number
- Store-specific service information
- Customer history integration

### 3. Enhanced Intent Recognition

**Current State:** Simple keyword matching
**Missing:**

- Advanced NLP intent classification
- Entity extraction (dates, times, services)
- Multi-turn conversation handling
- Context-aware responses

### 4. Error Handling

**Current State:** Basic error responses
**Missing:**

- Comprehensive error handling for API failures
- Fallback mechanisms for transcription errors
- Timeout handling for long AI processing
- Call quality monitoring

## Technical Architecture

### Database Schema

```sql
-- zn_calls table
CREATE TABLE zn_calls (
  id UUID PRIMARY KEY,
  clientCallId VARCHAR NOT NULL,     -- Twilio Call SID
  fromNumber VARCHAR NOT NULL,       -- Caller phone
  toNumber VARCHAR NOT NULL,         -- Store phone
  userId VARCHAR NULL,               -- Associated user
  storeId VARCHAR NULL,              -- Associated store
  assistantId VARCHAR NULL,          -- AI assistant
  threadId VARCHAR NULL,             -- OpenAI thread
  createdAt TIMESTAMP,
  updatedAt TIMESTAMP,
  deletedAt TIMESTAMP NULL
);

-- zn_call_messages table
CREATE TABLE zn_call_messages (
  id UUID PRIMARY KEY,
  callId VARCHAR NOT NULL,           -- Reference to zn_calls
  userId VARCHAR NULL,               -- Message sender
  message TEXT NULL,                 -- User speech (transcribed)
  reply TEXT NULL,                   -- AI response
  meta TEXT NULL,                    -- Additional metadata
  createdAt TIMESTAMP,
  updatedAt TIMESTAMP,
  deletedAt TIMESTAMP NULL
);
```

### API Endpoints

```
POST /v1/receptions/                    # Call initiation (Twilio webhook)
POST /v1/receptions/process-flow        # Voice processing (Twilio webhook)
POST /v1/receptions/process-reception/:id # AI response handling (Twilio webhook)
```

### Dependencies

- **Twilio**: Voice communication and TwiML generation
- **OpenAI**: Whisper (speech-to-text) and GPT (conversation)
- **Axios**: HTTP client for audio download
- **Node.js fs**: File system operations for audio handling

## Recommendations for Enhancement

### 1. Appointment Booking Integration

- Integrate with existing `AppointmentService`
- Add natural language date/time parsing
- Implement service selection through voice
- Add booking confirmation flow

### 2. Store Context Enhancement

- Map phone numbers to stores in database
- Load store-specific information during calls
- Implement store hours checking
- Add store-specific service offerings

### 3. Customer Management Integration

- Implement customer lookup by phone number
- Create customers automatically if not found
- Load customer history and preferences
- Personalize responses based on customer data

### 4. Advanced AI Features

- Implement proper intent classification
- Add entity extraction for dates, times, services
- Enhance conversation context management
- Add multi-language support

This analysis shows that while the foundation for voice-based AI reception is solid, significant enhancements are needed to integrate with the appointment booking system and provide a complete customer experience.

---

# AI Booking Integration Analysis

## Current System Architecture

### Appointment Management System

#### ZnAppointment Model

```typescript
// app/models/store_service/zn_appointment.ts
export enum EAppointmentStatus {
  BOOKED = 'booked',
  CONFIRMED = 'confirmed',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
  CHECKED_IN = 'checked-in',
  IN_SERVICE = 'in-service',
}

class ZnAppointment {
  storeId: string
  customerId: string
  status: EAppointmentStatus
  startTime: DateTime
  endTime: DateTime
  notes: string
  taxId: string
  subtotal: number
  discount: number
  taxAmount: number
  tipAmount: number
  total: number

  // N-N relationships
  services: ManyToMany<ZnStoreService>
  packages: ManyToMany<ZnStorePackage>
}
```

#### AppointmentService Business Logic

**Key Capabilities:**

- **CRUD Operations**: Create, read, update, delete appointments
- **Status Management**: 6-status workflow (booked → confirmed → checked-in → in-service → completed)
- **Pricing Calculations**: Automatic subtotal, tax, tip, and total calculations
- **Service/Package Association**: N-N relationships with dynamic pricing
- **Filtering**: By store, customer, date range, status
- **Time Management**: UTC conversion and date range queries

### Store Service Management

#### ZnStoreService Model

```typescript
// app/models/store_service/zn_store_service.ts
class ZnStoreService {
  name: string
  price: number
  duration: number // Service duration in minutes
  storeId: string
  imageId: string

  // Relationships
  categories: ManyToMany<ZnStoreServiceCategory>
  packages: ManyToMany<ZnStorePackage>
  store: BelongsTo<ZnStore>
}
```

#### ZnStorePackage Model

```typescript
// app/models/store_service/zn_store_package.ts
class ZnStorePackage {
  name: string
  storeId: string
  imageId: string

  // N-N with services and custom pricing
  services: ManyToMany<ZnStoreService>
}
```

**Service Management Features:**

- **Categorization**: Services organized by categories per store
- **Package Bundles**: Multiple services grouped with custom pricing
- **Duration Tracking**: Each service has duration for scheduling
- **Store-Specific**: All services belong to specific stores
- **Pricing Flexibility**: Services can have different prices in packages

### Customer Management System

#### ZnUser Model (Customer)

```typescript
// app/models/zn_user.ts
class ZnUser {
  firstName: string
  lastName: string
  email: string
  phone: string
  active: boolean
  timezone: string

  // N-N relationship with stores
  customerStores: ManyToMany<ZnStore>
}
```

#### CustomerService Business Logic

**Key Features:**

- **Auto-Creation**: Creates customers from email/phone if not exists
- **Store Association**: Automatic N-N relationship management
- **Lookup Capabilities**: Find by email or phone
- **Search Functionality**: Search customers by name, email, phone
- **Update Management**: Merge existing customer data

### Store Context Management

#### ZnStore Model

```typescript
// app/models/zn_store.ts
class ZnStore {
  phoneNumber: string
  email: string
  isManageBookingEnabled: boolean
  workingHour: TStoreWorkingHour[]
  timezone: string

  // N-N relationship with customers
  customers: ManyToMany<ZnUser>
}

type TStoreWorkingHour = {
  name: string // Day name (Monday, Tuesday, etc.)
  from: string // Opening time
  to: string // Closing time
  isOpen: boolean // Whether store is open this day
}
```

## AI Assistant Integration Points

### Current AI Assistant Roles

```typescript
// app/models/zn_ai_assistant.ts
export enum EAIAssistantRole {
  CUSTOMER_SERVICE = 'customer_service',
  SHOPPING_ASSISTANT = 'shopping_assistant',
  ORDER_ASSISTANT = 'order_assistant',
  RECEPTIONIST_SERVICE = 'receptionist_service', // ← Available but underutilized
}
```

### ZurnoAssistantService Integration

**Current Capabilities:**

- **Multi-Agent Orchestration**: Routes messages to appropriate AI assistant
- **Context Management**: Maintains OpenAI thread conversations
- **Product Integration**: Shopping assistant with product recommendations
- **Order Integration**: Order assistant with order history context

**Missing for Booking:**

- **Appointment Context**: No integration with appointment system
- **Store Context**: No store-specific information loading
- **Customer Context**: No customer history integration
- **Service Selection**: No voice-based service/package selection

## Integration Architecture for AI Booking

### Enhanced AI Receptionist Flow

```mermaid
sequenceDiagram
    participant Caller
    participant Twilio
    participant AIReceptionist
    participant StoreService
    participant CustomerService
    participant AppointmentService
    participant AIAssistant

    Caller->>Twilio: Phone call to store number
    Twilio->>AIReceptionist: Webhook with fromNumber/toNumber
    AIReceptionist->>StoreService: Lookup store by toNumber
    AIReceptionist->>CustomerService: Lookup customer by fromNumber
    AIReceptionist->>AIAssistant: Create/resume conversation thread

    loop Conversation Flow
        Caller->>Twilio: Voice input
        Twilio->>AIReceptionist: Transcribed speech
        AIReceptionist->>AIAssistant: Process with store/customer context

        alt Appointment Request
            AIAssistant->>StoreService: Get available services
            AIAssistant->>AppointmentService: Check availability
            AIAssistant->>CustomerService: Create/update customer
            AIAssistant->>AppointmentService: Create appointment
        else General Inquiry
            AIAssistant->>AIAssistant: Handle general questions
        end

        AIAssistant->>AIReceptionist: Generate response
        AIReceptionist->>Twilio: Convert to speech
        Twilio->>Caller: Voice response
    end
```

### Required Enhancements

#### 1. Enhanced ZurnoAIReceptionistService

**New Methods Needed:**

```typescript
class ZurnoAIReceptionistService {
  // Store context loading
  async loadStoreContext(toNumber: string): Promise<ZnStore | null>

  // Customer context loading
  async loadCustomerContext(fromNumber: string, storeId: string): Promise<ZnUser | null>

  // Service selection through voice
  async handleServiceSelection(storeId: string, userInput: string): Promise<ZnStoreService[]>

  // Appointment booking flow
  async handleAppointmentBooking(
    storeId: string,
    customerId: string,
    services: string[],
    dateTime: string
  ): Promise<ZnAppointment>

  // Availability checking
  async checkAvailability(storeId: string, startTime: DateTime, duration: number): Promise<boolean>
}
```

#### 2. Enhanced AI Assistant Context

**RECEPTIONIST_SERVICE Role Enhancement:**

```typescript
// Enhanced system prompt for appointment booking
const receptionistSystemPrompt = `
You are ZURNO RECEPTIONIST SERVICE, an AI assistant for ${storeName}.

STORE CONTEXT:
- Store: ${storeName}
- Phone: ${storePhone}
- Working Hours: ${workingHours}
- Available Services: ${availableServices}
- Available Packages: ${availablePackages}

CUSTOMER CONTEXT:
- Customer: ${customerName || 'New Customer'}
- Phone: ${customerPhone}
- Previous Appointments: ${appointmentHistory}

CAPABILITIES:
1. Schedule appointments with available services
2. Check availability for requested times
3. Provide service information and pricing
4. Handle customer inquiries about store hours
5. Transfer to human agent when needed

APPOINTMENT BOOKING FLOW:
1. Identify customer (create if new)
2. Understand service requirements
3. Suggest available time slots
4. Confirm appointment details
5. Create booking and provide confirmation

Always be helpful, professional, and accurate with store information.
`
```

#### 3. Natural Language Processing Enhancements

**Intent Recognition:**

```typescript
interface BookingIntent {
  type: 'appointment_booking' | 'service_inquiry' | 'availability_check'
  services?: string[]
  packages?: string[]
  preferredDate?: string
  preferredTime?: string
  duration?: number
}

class BookingNLPService {
  async extractBookingIntent(userInput: string, storeContext: ZnStore): Promise<BookingIntent>
  async parseDateTime(dateTimeString: string, timezone: string): Promise<DateTime>
  async matchServices(serviceNames: string[], storeId: string): Promise<ZnStoreService[]>
}
```

#### 4. Availability Management

**Time Slot Calculation:**

```typescript
class AvailabilityService {
  async getAvailableSlots(
    storeId: string,
    date: DateTime,
    serviceDuration: number
  ): Promise<TimeSlot[]>

  async checkConflicts(
    storeId: string,
    startTime: DateTime,
    endTime: DateTime
  ): Promise<ZnAppointment[]>

  async suggestAlternativeTimes(
    storeId: string,
    requestedTime: DateTime,
    duration: number
  ): Promise<TimeSlot[]>
}

interface TimeSlot {
  startTime: DateTime
  endTime: DateTime
  available: boolean
}
```

### Implementation Roadmap

#### Phase 1: Basic Integration

1. **Store Context Loading**: Map phone numbers to stores
2. **Customer Identification**: Lookup/create customers by phone
3. **Service Information**: Provide service details through voice
4. **Basic Appointment Creation**: Simple booking with manual time entry

#### Phase 2: Advanced Features

1. **Natural Language Date/Time**: Parse "next Tuesday at 2 PM"
2. **Availability Checking**: Real-time slot availability
3. **Service Selection**: Voice-based service/package selection
4. **Conflict Resolution**: Handle scheduling conflicts

#### Phase 3: Smart Features

1. **Intelligent Scheduling**: AI-suggested optimal time slots
2. **Customer Preferences**: Learn from booking history
3. **Multi-Service Booking**: Handle complex appointment requests
4. **Proactive Notifications**: Appointment reminders and confirmations

This enhanced architecture would transform the current basic AI receptionist into a fully functional booking system capable of handling complex appointment scheduling through natural voice conversations.
