# Zurno AI Receptionist Flow Analysis

## Overview

The Zurno AI Receptionist system is a voice-based AI assistant that handles incoming phone calls using Twilio integration, OpenAI Whisper for speech-to-text, and OpenAI GPT for natural language processing. The system manages call sessions, processes voice input, and provides intelligent responses.

## Core Components

### 1. Models

#### ZnCall Model
```typescript
// app/models/zn_call.ts
class ZnCall {
  clientCallId: string    // Twilio Call SID
  fromNumber: string      // Caller's phone number
  toNumber: string        // Store's phone number
  userId: string          // Associated user ID (nullable)
  storeId: string         // Associated store ID (nullable)
  assistantId: string     // AI Assistant ID (nullable)
  threadId: string        // OpenAI Thread ID for conversation context
}
```

#### ZnCallMessage Model
```typescript
// app/models/zn_call_message.ts
class ZnCallMessage {
  callId: string          // Reference to ZnCall
  userId: string          // User who sent the message (nullable)
  message: string         // Transcribed user speech
  reply: string           // AI-generated response
}
```

### 2. Services

#### ZurnoAIReceptionistService
**Location:** `app/services/zurno_ai_receptionist_service.ts`

**Key Methods:**
- `sayAndListen(message)`: Generates Twilio TwiML for speech synthesis and recording
- `translateVoiceToText(recordingUrl)`: Downloads and transcribes audio using OpenAI Whisper
- `handleReception(room, message, userSaid)`: Processes user input through AI assistant
- `handleMakeAppointment()`: Handles appointment booking requests
- `redialToAgent()`: Transfers call to human agent
- `handlePause(userMessage)`: Implements waiting mechanism for AI processing

### 3. Controllers

#### AiReceptionistsController
**Location:** `app/controllers/app/ai_receptionists_controller.ts`

**Endpoints:**
- `POST /v1/receptions/` - Welcome endpoint (call initiation)
- `POST /v1/receptions/process-flow` - Main conversation flow
- `POST /v1/receptions/process-reception/:id` - AI response processing

## Detailed Flow Analysis

### 1. Call Initiation Flow (`welcome` method)

```mermaid
sequenceDiagram
    participant Twilio
    participant Controller
    participant ZnCall
    participant OpenAI
    participant Service

    Twilio->>Controller: POST /receptions/ (CallSid, From, To)
    Controller->>ZnCall: Query previous call by fromNumber/toNumber
    alt Previous call exists
        ZnCall-->>Controller: Return existing threadId
    else No previous call
        Controller->>OpenAI: Create new thread
        OpenAI-->>Controller: Return threadId
    end
    Controller->>ZnCall: Create new call record
    Controller->>Service: sayAndListen("Welcome message")
    Service-->>Controller: Return TwiML response
    Controller->>Twilio: Send TwiML (voice + record)
```

**Key Features:**
- **Session Management**: Maintains conversation context using OpenAI threads
- **Call Tracking**: Creates ZnCall record for each incoming call
- **Continuity**: Reuses existing thread for repeat callers
- **Welcome Message**: "Hi, I am Zurno AI Receptionist. How can I help you?"

### 2. Voice Processing Flow (`processFlow` method)

```mermaid
sequenceDiagram
    participant Twilio
    participant Controller
    participant Service
    participant OpenAI
    participant ZnCallMessage

    Twilio->>Controller: POST /process-flow (CallSid, RecordingUrl)
    Controller->>Service: translateVoiceToText(recordingUrl)
    Service->>Service: waitForTwilioAudio() - Download with retry
    Service->>OpenAI: Whisper transcription
    OpenAI-->>Service: Return transcribed text
    Service-->>Controller: Return user speech
    Controller->>ZnCallMessage: Create message record
    
    alt Contains "appointment"
        Controller->>Service: handleMakeAppointment()
    else Contains "bye"
        Controller->>Service: handleBye()
    else Contains "agent"
        Controller->>Service: redialToAgent()
    else General query
        Controller->>Service: handleReception() + handlePause()
    end
    
    Service-->>Controller: Return TwiML response
    Controller->>Twilio: Send TwiML
```

**Key Features:**
- **Speech-to-Text**: Uses OpenAI Whisper for accurate transcription
- **Intent Recognition**: Simple keyword-based routing
- **Message Persistence**: Stores all user messages in database
- **Robust Audio Handling**: Retry mechanism for Twilio audio download

### 3. AI Response Processing Flow (`processReceptionist` method)

```mermaid
sequenceDiagram
    participant Twilio
    participant Controller
    participant ZnCallMessage
    participant Service

    Twilio->>Controller: POST /process-reception/:id
    Controller->>ZnCallMessage: Query message by ID
    alt AI reply ready
        ZnCallMessage-->>Controller: Return message with reply
        Controller->>Service: sayAndListen(reply)
        Service-->>Controller: Return TwiML with loop redirect
    else AI still processing
        Controller->>Service: handlePause()
        Service-->>Controller: Return TwiML with 5s pause + retry
    end
    Controller->>Twilio: Send TwiML
```

**Key Features:**
- **Asynchronous Processing**: Handles AI response delays gracefully
- **Polling Mechanism**: Retries every 5 seconds until AI response ready
- **Conversation Loop**: Redirects back to main flow after response

### 4. AI Integration (`handleReception` method)

```mermaid
sequenceDiagram
    participant Service
    participant ZnAIAssistant
    participant ZurnoAssistantService
    participant OpenAI
    participant ZnCallMessage

    Service->>ZnAIAssistant: Find CUSTOMER_SERVICE assistant
    Service->>ZurnoAssistantService: getAIAnswer(assistant, threadId, userSaid)
    ZurnoAssistantService->>OpenAI: Process through assistant
    OpenAI-->>ZurnoAssistantService: Return AI response
    ZurnoAssistantService-->>Service: Return parsed response
    Service->>ZnCallMessage: Update message.reply
    ZnCallMessage-->>Service: Save to database
```

## Current Implementation Gaps

### 1. Appointment Booking Integration
**Current State:** Basic keyword detection for "appointment"
**Missing:**
- Integration with appointment booking system
- Natural language date/time parsing
- Service selection through voice
- Customer identification and creation
- Availability checking
- Booking confirmation

### 2. Store/Customer Context
**Current State:** Stores fromNumber/toNumber but doesn't use them
**Missing:**
- Store identification by phone number
- Customer lookup by phone number
- Store-specific service information
- Customer history integration

### 3. Enhanced Intent Recognition
**Current State:** Simple keyword matching
**Missing:**
- Advanced NLP intent classification
- Entity extraction (dates, times, services)
- Multi-turn conversation handling
- Context-aware responses

### 4. Error Handling
**Current State:** Basic error responses
**Missing:**
- Comprehensive error handling for API failures
- Fallback mechanisms for transcription errors
- Timeout handling for long AI processing
- Call quality monitoring

## Technical Architecture

### Database Schema
```sql
-- zn_calls table
CREATE TABLE zn_calls (
  id UUID PRIMARY KEY,
  clientCallId VARCHAR NOT NULL,     -- Twilio Call SID
  fromNumber VARCHAR NOT NULL,       -- Caller phone
  toNumber VARCHAR NOT NULL,         -- Store phone
  userId VARCHAR NULL,               -- Associated user
  storeId VARCHAR NULL,              -- Associated store
  assistantId VARCHAR NULL,          -- AI assistant
  threadId VARCHAR NULL,             -- OpenAI thread
  createdAt TIMESTAMP,
  updatedAt TIMESTAMP,
  deletedAt TIMESTAMP NULL
);

-- zn_call_messages table
CREATE TABLE zn_call_messages (
  id UUID PRIMARY KEY,
  callId VARCHAR NOT NULL,           -- Reference to zn_calls
  userId VARCHAR NULL,               -- Message sender
  message TEXT NULL,                 -- User speech (transcribed)
  reply TEXT NULL,                   -- AI response
  meta TEXT NULL,                    -- Additional metadata
  createdAt TIMESTAMP,
  updatedAt TIMESTAMP,
  deletedAt TIMESTAMP NULL
);
```

### API Endpoints
```
POST /v1/receptions/                    # Call initiation (Twilio webhook)
POST /v1/receptions/process-flow        # Voice processing (Twilio webhook)
POST /v1/receptions/process-reception/:id # AI response handling (Twilio webhook)
```

### Dependencies
- **Twilio**: Voice communication and TwiML generation
- **OpenAI**: Whisper (speech-to-text) and GPT (conversation)
- **Axios**: HTTP client for audio download
- **Node.js fs**: File system operations for audio handling

## Recommendations for Enhancement

### 1. Appointment Booking Integration
- Integrate with existing `AppointmentService`
- Add natural language date/time parsing
- Implement service selection through voice
- Add booking confirmation flow

### 2. Store Context Enhancement
- Map phone numbers to stores in database
- Load store-specific information during calls
- Implement store hours checking
- Add store-specific service offerings

### 3. Customer Management Integration
- Implement customer lookup by phone number
- Create customers automatically if not found
- Load customer history and preferences
- Personalize responses based on customer data

### 4. Advanced AI Features
- Implement proper intent classification
- Add entity extraction for dates, times, services
- Enhance conversation context management
- Add multi-language support

This analysis shows that while the foundation for voice-based AI reception is solid, significant enhancements are needed to integrate with the appointment booking system and provide a complete customer experience.
