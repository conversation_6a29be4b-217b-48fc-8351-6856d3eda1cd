# Zurno API

## Overview

This project is built using [AdonisJS](https://adonisjs.com/), a Node.js web framework that provides a robust and elegant structure for developing server-side applications. AdonisJS follows the MVC (Model-View-Controller) pattern and is inspired by frameworks like Lara<PERSON>, making it familiar for developers with experience in similar ecosystems.

## Requirements

- Node.js (>= 20.x)
- npm (>= 10.x)
- AdonisJS CLI (optional, but recommended)

## Installation

1. **Clone the repository:**

   ```bash
   git clone https://gitlab.softyn.com/web/zurno-api
   cd zurno-api
   ```

2. **Install dependencies**

   ```bash
   npm install
   or
   yarn install
   ```

3. **Copy env file**

   ```bash
   cp .env.example .env
   ```

4. **Usage**
   ```bash
   adonis serve --dev
   or
   npm run dev
   or
   yarn dev
   ```
5. **Initialize Database**

   ```bash
   node ace migration:fresh

   node ace db:seed

   node ace permission:fresh
   ```

## Permissions

This application use RBAC to manage permissions, which are stored in database.

### Initialize

Prepare the initial permissions using the following command.

```bash
node ace permission:fresh
```

This command drop all records in the permissions table, then runs all files in the <code>database/permissions</code> directory.

### Usage

```
import { HttpContext } from '@adonisjs/core/http'

export default class PostController {
   async create({ bouncer }: HttpContext) {
      await bouncer.authorize('allow', "create:post")

      // continue with controller logic
   }
}
```

### Adding permissions

New permissions can be created using the following command.

```bash
node ace make:permission new_permission

# create database/permissions/1731422153245_new_permission.ts
```

This command creates a new file inside the <code>database/permissions</code> director, prefixed with the current timestamp so that the files are sorted in the order created, and with the following content.

```
import AuthorizationService from '#services/authorization_service'

export default class {
   async up() {
      await AuthorizationService.createPermission("action", "resource")
   }

   async down() {
      await AuthorizationService.deletePermission("action", "resource")
   }
}
```

- The <code>up</code> method is used to modify the permissions table. Usually, you will create new permission inside this method.
- The <code>down</code> method is used to roll back the actions executed by the up method. For example, if the up method creates a permission, the down method should delete the same permission.

### Operations

**Run**  
 Once you have created the files you need, you can run the following command to proceed.

```bash
node ace permission:run
```

This command executes the <code>up</code> method on all files created **after** the latest file on record.

**Status**  
 Executed files are stored inside the <code>zn_permissions_control</code>, and can be checked using the following command

```bash
node ace permission:status
```

**Rollback**  
 You can roll back by running the <code>permission:rollback</code> command. The rollback action is performed on the files from the most recent batch. However, you can also specify a custom batch number where you want to roll back.

```bash
# Rollback to the previous batch
node ace migration:rollback

# Rollback to the start
node ace migration:rollback 0

# Rollback to batch 1
node ace migration:rollback 1
```

The rollback command executes the <code>down</code> method on permission files in reversed order.

**Reset**

```bash
node ace permission:reset
```

This command executes the <code>down</code> method on all files created **before** the lastest recorded file in reversed order.

## AI Search

```bash
run sql file from database/create_function_cosine_similarity.sql
```

## Streaming

This project makes use of IVS services from Amazon to create streams.

### IVS Player

In order to receive stream information, see [Amazon IVS Player](https://docs.aws.amazon.com/ivs/latest/LowLatencyUserGuide/player.html) documents.

The player also has the capability to receive [metadata](https://docs.aws.amazon.com/ivs/latest/LowLatencyUserGuide/metadata.html).

The metadata sent is freeform, so data from Zurno will generally look like:

``` js
{
   event: STREAM_EVENT,
   timestamp: 1740585121412,
   // other data
}
```

STREAM_EVENT types:

<code>variant.start</code>: Start showing Product Variant

Due to Amazon IVS metadata having a 1KB request limit (around 1000 characters), the product variant resource will be limited to important fields only.
``` js
{
   event: "variant.start",
   timestamp: 1740585121412,
   variant: {
        id: "VARIANT_UUID",
        shopifyVariantId: "gid://shopify/ProductVariant/SHOPIFY_VARIANT_ID",
        title: "VARIANT_TITLE",
        price: "VARIANT_PRICE",
        compareAtPrice: "VARIANT_COMPARE_AT_PRICE",
        product: {
            id: "PRODUCT_ID",
            title: "PRODUCT_TITLE",
            shopifyProductId: "gid://shopify/Product/SHOPIFY_PRODUCT_ID",
            reviewSummary: {
                averageRating: "PRODUCT_AVERAGE_RATING"
            }
        },
        image: {
            src: "IMAGE_SOURCE"
        }
    }
}
```
If the metadata exceeds the limit, it will be truncated to only the variant id:
``` js
{
   event: "variant.start",
   timestamp: 1740585121412,
   variant: {
      id: "VARIANT_UUID"
   }
}
```

<code>variant.end</code>: Stop showing Product Variant
``` js
{
   event: "variant.end",
   timestamp: 1740585121412,
   variant: {
      id: "VARIANT_UUID"
   }
}
```

<code>viewer.count</code>: Current viewer count
``` js
{
   event: "viewer.count",
   timestamp: 1740585121412,
   viewerCount: 100.
}
```


### IVS Chat

The project makes use of [Amazon IVS Chat](https://docs.aws.amazon.com/ivs/latest/ChatUserGuide/what-is.html) in order to create a live chat room.

## Set Up Chat Bot

The live-shopping chat-bot requires **two steps**, executed **in order**:

1. **Populate or refresh the product-embedding vector database**  
   (generates OpenAI embeddings for every active product and upserts them into Pinecone).
2. **Configure your OpenAI assistants** (one per `EAIAssistantRole`) so they can answer product questions in real time.

> **Important:** Run step&nbsp;1 first.  
> If you skip it, the assistants will have no product context.

---

### 1 Update the Vector Database

````
# queues must be running
# update all (default behaviour)
node ace update:vector-database
node ace update:vector-database --all
node ace update:vector-database -a

# one target
node ace update:vector-database -t products
node ace update:vector-database -t collections

# multiple targets – either way works
node ace update:vector-database -t products -t collections
node ace update:vector-database -t "products,collections"
````

#### Required `.env` keys

| Key                                        | Purpose                                   |
| ------------------------------------------ | ----------------------------------------- |
| `OPENAI_API_KEY`                           | Generates embeddings                      |
| `PINECONE_API_KEY`                         | Pinecone authentication                   |
| `PROD_EMBEDDING_INDEX_NAME`                | Name of the vector index                  |

---

### 2 Set Up OpenAI Assistants

```bash
# after embeddings are in place
node ace update:setup_ai_assistant
```

#### Required `.env` keys

Add one entry **per** assistant role (use the exact enum name in uppercase):

```env
CUSTOMER_SERVICE=asst_************************
SHOPPING_ASSISTANT=asst_************************
# …additional roles
```

---


