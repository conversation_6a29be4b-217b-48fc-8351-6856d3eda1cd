import ace from '@adonisjs/core/services/ace'
import type { ApplicationService } from '@adonisjs/core/types'
import cron from 'node-cron'

export default class CronProvider {
  constructor(protected app: ApplicationService) {}

  /**
   * Register bindings to the container
   */
  register() {}

  /**
   * The container bindings have booted
   */
  async boot() {}

  /**
   * The application has been booted
   */
  async start() {}

  /**
   * The process has been started
   */
  async ready() {
    const isMigrationCommand = process.argv.some((arg) =>
      ['migration:run', 'migration:refresh', 'migration:rollback', 'migration:status'].includes(arg)
    )

    if (!isMigrationCommand && process.env.CRON_JOB_ENABLE === 'true') {
      this.startJobs()
    }

    //Clearn stalled jobs
    // cron.schedule('*/20 * * * *', async () => {
    //   console.log('Running every 20 mins', new Date())
    //   await ace.exec('clear:stalled-job', [])
    // })
  }
  /**
   * Preparing to shutdown the app
   */
  async shutdown() {
    cron.getTasks().forEach((job) => job.stop())
  }

  private startJobs() {
    cron.schedule('0 5 * * *', async () => {
      console.log('Cron job running every day', new Date())
      await ace.exec('update:expire-post', [])
      await ace.exec('update:posts', [])

      //Run command
      await ace.exec('shopify:sync-collections', [])
      await ace.exec('shopify:sync-products', [])
      await ace.exec('sync:product-review', [])
      await ace.exec('shopify:sync-all-fastbundle-product', [])

      //Update user favorite
      await ace.exec('app:update-user-favorite', [])
    })

    cron.schedule('0 8 * * *', async () => {
      console.log('Running scheduled job at 8 AM')
      await ace.exec('sendEmail:notification-low-stock-inventory', [])
      await ace.exec('sendEmail:unfulfil-orders', [])
    })

    // cron.schedule('0 0 * * 1', async () => {
    //   console.log('Schedule to run every Monday at 12:00 AM')
    //   await ace.exec('send:email-user-classified-report', [])
    //   console.log('Queued report jobs for all users')
    // })

    cron.schedule('0 6 * * *', async () => {
      console.log('Running daily job to send monthly remind', new Date())
      //Remind verify store
      await ace.exec('store:remind-verify', [])
    })

    cron.schedule('*/30 * * * *', async () => {
      console.log('Running every 30 mins', new Date())
      await ace.exec('send:campaign-mails', [])
    })

    cron.schedule('*/15 * * * *', async () => {
      console.log('Running every 15 mins', new Date())
      await ace.exec('send:campaigns', [])
      await ace.exec('send:posts', [])
      // await ace.exec('clean:streams', [])
    })

    // Update post popularity cache every 10 minutes
    cron.schedule('*/10 * * * *', async () => {
      await ace.exec('update:post-popularity-cache', [])
    })

    cron.schedule('*/2 * * * *', async () => {
      console.log('Running every 2 mins', new Date())
      await ace.exec('delete:draft-order-expired', [])
    })
  }
}
