import UpdateProductEmbeddingJob from '#jobs/update_product_embedding_job'
import queue from '@rlanz/bull-queue/services/main'
import {BaseCommand, flags} from '@adonisjs/core/ace'
import type { CommandOptions } from '@adonisjs/core/types/ace'
import ZnProduct from '#models/zn_product'
import ZnCollection from "#models/zn_collection";
import UpdateCollectionEmbeddingJob from "#jobs/update_collection_embedding_job";

export default class UpdateVectorDatabase extends BaseCommand {
  static commandName = 'update:vector-database'
  static description = 'Update the Pinecone vector store with all active products'

  static options: CommandOptions = { startApp: true }

  @flags.array({
    name: 'target',
    alias : 't',
    description :'Targets to update: products, collections (repeat flag or CSV)',
  })
  public target : string[] = []

  @flags.boolean({
    name : 'all',
    alias : 'a',
    description :'All to update all collections',
  })

  public all!: boolean

  private targets = [
    {
      key: 'products',
      query: () => ZnProduct.query().where('status','active'),
      job: UpdateProductEmbeddingJob,
      label: 'products'
    },
    {
      key : 'collections',
      query : ()  => ZnCollection.query().where('status','active'),
      job: UpdateCollectionEmbeddingJob,
      label: 'collections'
    }
  ] as const

  async run() {
    try {
      const selected = (this.target ?? []).map(selection => selection.toLowerCase().trim()).filter(Boolean)

      const wantedKeys =
        this.all || selected.length === 0
          ? this.targets.map((target) => target.key)   // update everything
          : selected

      for (const target of this.targets) {
        if (!wantedKeys.includes(target.key)) continue

        const rows = await target.query()
        const ids = (rows ?? []).map(record => record.id)

        if (!ids.length) {
          this.logger.info(`No active ${target.label} found`)
          continue
        }


        await queue.dispatch(target.job, {ids}, {queueName: 'syncData'})
        this.logger.info(`Queued ${ids.length} ${target.label} queued`)

      }
    } catch (error) {
      this.logger.error(error)
      this.exitCode = 1
    }
  }

}
