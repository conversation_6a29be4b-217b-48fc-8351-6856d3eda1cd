import {BaseCommand} from '@adonisjs/core/ace'
import type {CommandOptions} from '@adonisjs/core/types/ace'
import ZnProduct from "#models/zn_product";
import {YoutubeService} from "#services/google/youtube_service";
import ZnPost, {EPostSource, EPostType} from "#models/zn_post";
import ZnMedia from "#models/zn_media";
import {MEDIA_SOURCE, MEDIA_TYPE} from "#constants/media";
import ZnVideoTimeline from "#models/zn_video_timeline";
import ZnProductVariant from "#models/zn_product_variant";
import {StoreService} from "#services/store_service";

export default class ExportVideoProduct extends BaseCommand {
  static commandName = 'export:video-product'
  static description = ''

  static options: CommandOptions = {
    startApp: true
  }

  async run() {
    this.logger.info('Create video post from how to video')
    const youtubeService = new YoutubeService()
    const storeService = new StoreService()

    const youtubeRegex = /(?:https?:\/\/)?(?:www\.)?(?:youtube\.com\/(?:watch\?v=|embed\/)|youtu\.be\/)([\w\-]{11})/g;
    const products = await ZnProduct.query().preload('variants')
    for (const product of products) {
      const description = product.description
      if(description) {
        const matches = [...description.matchAll(youtubeRegex)].map(m => m[1])

        if (matches) {
          for (const videoId of matches) {
            const video = await youtubeService.getYoutubeMeta(videoId)
            // @ts-ignore
            const existMedia = await ZnMedia.query({ mode: 'write'}).where({resourceId: videoId, sourceFrom: MEDIA_SOURCE.YOUTUBE}).first()
            if(video && !existMedia) {
              const zurnoStore = await storeService.getZurnoStore()
              const createdThumbnail = await ZnMedia.create({
                fileKey: video.thumbnail,
                url: video.thumbnail,
                type: MEDIA_TYPE.IMAGE
              })

              const media = await ZnMedia.create({
                fileKey: video.url,
                url: video.url,
                type: MEDIA_TYPE.VIDEO,
                sourceFrom: MEDIA_SOURCE.YOUTUBE
              })
                //Create post
              const post = await ZnPost.create({
                title: video?.title,
                description: video?.description,
                thumbnailId: createdThumbnail?.id,
                type: EPostType.VIDEO,
                source: EPostSource.ZURNO,
                storeId: zurnoStore?.id || null
              })
              //Sync media
              await post.related('medias').sync([media.id])

              //Add products to timeline
              let timelines = await youtubeService.parseTimelineFromDescription(video.description)
              if(timelines.length == 0) {
                timelines = [
                  {
                    start: 0,
                    end: video.end,
                    variantId: 'null'
                  }
                ]
              }
              console.log(timelines)
              for (const timeline of timelines) {
                let variant = await ZnProductVariant.query().where('shopifyVariantId', `gid://shopify/ProductVariant/${timeline.variantId}`).first()
                if(!variant) {
                  variant = product.variants[
                    Math.floor(Math.random() * product.variants.length)
                    ]
                }

                if(variant) {
                  await ZnVideoTimeline.create(
                    {
                      postId: post.id,
                      variantId: variant.id,
                      start: timeline.start,
                      end: timeline?.end || video.end,
                    }
                  )
                }
              }
              break;
            }
          }
        }
      }
    }
  }
}
