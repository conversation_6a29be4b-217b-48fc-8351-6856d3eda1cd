#!/usr/bin/env node

/**
 * Zurno AI Booking System Test Suite
 *
 * This script tests the AI booking system endpoints and functionality
 * Run with: node test_ai_booking_system.js
 */

const axios = require('axios')
const fs = require('fs')

// Configuration
const BASE_URL = process.env.TEST_BASE_URL || 'http://localhost:3333'
const API_BASE = `${BASE_URL}/v1/receptions`

// Test data
const TEST_STORE_PHONE = process.env.TEST_STORE_PHONE || '+1987654321'
const TEST_CUSTOMER_PHONE = process.env.TEST_CUSTOMER_PHONE || '+1234567890'

class AIBookingSystemTester {
  constructor() {
    this.testResults = []
    this.callSid = `test_${Date.now()}`
  }

  async runAllTests() {
    console.log('🚀 Starting Zurno AI Booking System Tests\n')

    try {
      await this.testWelcomeEndpoint()
      await this.testStoreNotFound()
      await this.testProcessFlowBasic()
      await this.testAppointmentBookingFlow()
      await this.testServiceInquiry()
      await this.testErrorHandling()

      this.printResults()
    } catch (error) {
      console.error('❌ Test suite failed:', error.message)
    }
  }

  async testWelcomeEndpoint() {
    console.log('📞 Testing Welcome Endpoint...')

    try {
      const response = await axios.post(
        `${API_BASE}/`,
        {
          CallSid: this.callSid,
          From: TEST_CUSTOMER_PHONE,
          To: TEST_STORE_PHONE,
        },
        {
          headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        }
      )

      if (response.status === 200 && response.data.includes('AI receptionist')) {
        this.addResult('Welcome Endpoint', 'PASS', 'Successfully handled welcome call')
      } else {
        this.addResult('Welcome Endpoint', 'FAIL', `Unexpected response: ${response.data}`)
      }
    } catch (error) {
      this.addResult('Welcome Endpoint', 'FAIL', error.message)
    }
  }

  async testStoreNotFound() {
    console.log('🏪 Testing Store Not Found Scenario...')

    try {
      const response = await axios.post(
        `${API_BASE}/`,
        {
          CallSid: `${this.callSid}_nostore`,
          From: TEST_CUSTOMER_PHONE,
          To: '+1000000000', // Non-existent store phone
        },
        {
          headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        }
      )

      if (response.status === 200 && response.data.includes('could not identify')) {
        this.addResult('Store Not Found', 'PASS', 'Correctly handled missing store')
      } else {
        this.addResult('Store Not Found', 'FAIL', 'Should return store not found error')
      }
    } catch (error) {
      this.addResult('Store Not Found', 'FAIL', error.message)
    }
  }

  async testProcessFlowBasic() {
    console.log('🔄 Testing Process Flow Endpoint...')

    try {
      // Create a mock recording URL (this would normally be from Twilio)
      const mockRecordingUrl = 'https://api.twilio.com/2010-04-01/Accounts/test/Recordings/test.wav'

      const response = await axios.post(
        `${API_BASE}/process-flow`,
        {
          CallSid: this.callSid,
          RecordingUrl: mockRecordingUrl.replace('.wav', ''), // Twilio format
        },
        {
          headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        }
      )

      if (response.status === 200) {
        this.addResult('Process Flow Basic', 'PASS', 'Endpoint accessible')
      } else {
        this.addResult('Process Flow Basic', 'FAIL', `Status: ${response.status}`)
      }
    } catch (error) {
      // Expected to fail due to mock recording URL, but endpoint should be accessible
      if (error.response && error.response.status !== 404) {
        this.addResult(
          'Process Flow Basic',
          'PASS',
          'Endpoint accessible (expected recording error)'
        )
      } else {
        this.addResult('Process Flow Basic', 'FAIL', error.message)
      }
    }
  }

  async testAppointmentBookingFlow() {
    console.log('📅 Testing Appointment Booking Flow...')

    // This test simulates the booking conversation flow
    const bookingScenarios = [
      'I want to book an appointment',
      'Schedule me for a manicure',
      'What appointments do you have available tomorrow?',
    ]

    for (const scenario of bookingScenarios) {
      try {
        // In a real test, this would involve actual voice processing
        // For now, we test the structure and accessibility
        console.log(`  Testing scenario: "${scenario}"`)

        // Test would involve:
        // 1. Sending voice input
        // 2. Checking AI response
        // 3. Verifying intent recognition
        // 4. Confirming appointment creation

        this.addResult(`Booking Scenario: ${scenario}`, 'SKIP', 'Requires voice processing setup')
      } catch (error) {
        this.addResult(`Booking Scenario: ${scenario}`, 'FAIL', error.message)
      }
    }
  }

  async testServiceInquiry() {
    console.log('💅 Testing Service Inquiry...')

    // Test service information requests
    const serviceQuestions = [
      'What services do you offer?',
      'How much does a manicure cost?',
      'What are your hours?',
    ]

    for (const question of serviceQuestions) {
      console.log(`  Testing question: "${question}"`)
      this.addResult(`Service Question: ${question}`, 'SKIP', 'Requires voice processing setup')
    }
  }

  async testErrorHandling() {
    console.log('⚠️ Testing Error Handling...')

    try {
      // Test with invalid CallSid
      const response = await axios.post(
        `${API_BASE}/process-flow`,
        {
          CallSid: 'invalid_call_sid',
          RecordingUrl: 'invalid_url',
        },
        {
          headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        }
      )

      // Should handle gracefully
      if (response.status === 200) {
        this.addResult('Error Handling', 'PASS', 'Gracefully handled invalid input')
      } else {
        this.addResult('Error Handling', 'FAIL', 'Should handle invalid input gracefully')
      }
    } catch (error) {
      this.addResult('Error Handling', 'PASS', 'Error handled appropriately')
    }
  }

  addResult(testName, status, message) {
    this.testResults.push({ testName, status, message })
    const emoji = status === 'PASS' ? '✅' : status === 'FAIL' ? '❌' : '⏭️'
    console.log(`  ${emoji} ${testName}: ${message}\n`)
  }

  printResults() {
    console.log('\n📊 Test Results Summary')
    console.log('========================\n')

    const passed = this.testResults.filter((r) => r.status === 'PASS').length
    const failed = this.testResults.filter((r) => r.status === 'FAIL').length
    const skipped = this.testResults.filter((r) => r.status === 'SKIP').length

    console.log(`✅ Passed: ${passed}`)
    console.log(`❌ Failed: ${failed}`)
    console.log(`⏭️ Skipped: ${skipped}`)
    console.log(`📊 Total: ${this.testResults.length}\n`)

    if (failed > 0) {
      console.log('Failed Tests:')
      this.testResults
        .filter((r) => r.status === 'FAIL')
        .forEach((r) => console.log(`  - ${r.testName}: ${r.message}`))
    }

    // Save detailed results
    const reportPath = 'test_results.json'
    fs.writeFileSync(
      reportPath,
      JSON.stringify(
        {
          timestamp: new Date().toISOString(),
          summary: { passed, failed, skipped, total: this.testResults.length },
          results: this.testResults,
        },
        null,
        2
      )
    )

    console.log(`\n📄 Detailed results saved to: ${reportPath}`)
  }
}

// Database verification functions
async function verifyDatabaseSetup() {
  console.log('🗄️ Verifying Database Setup...\n')

  // These would be actual database queries in a real implementation
  const checks = [
    'Stores table has phone numbers configured',
    'Store services are available',
    'AI assistants are configured',
    'Required migrations are applied',
  ]

  checks.forEach((check) => {
    console.log(`  ⏭️ ${check}: Manual verification required`)
  })

  console.log('\n💡 To verify database setup, run these SQL queries:')
  console.log('  SELECT COUNT(*) FROM zn_stores WHERE phone IS NOT NULL;')
  console.log('  SELECT COUNT(*) FROM zn_store_services;')
  console.log('  SELECT COUNT(*) FROM zn_ai_assistants;')
  console.log('  SELECT COUNT(*) FROM zn_appointments;\n')
}

// Environment verification
function verifyEnvironment() {
  console.log('🌍 Verifying Environment...\n')

  const requiredEnvVars = ['OPENAI_API_KEY', 'REDIS_HOST', 'REDIS_PORT']

  const missing = requiredEnvVars.filter((env) => !process.env[env])

  if (missing.length > 0) {
    console.log(`❌ Missing environment variables: ${missing.join(', ')}`)
    console.log('   Please set these in your .env file\n')
  } else {
    console.log('✅ All required environment variables are set\n')
  }

  return missing.length === 0
}

// Main execution
async function main() {
  console.log('🧪 Zurno AI Booking System Test Suite')
  console.log('=====================================\n')

  // Verify environment first
  if (!verifyEnvironment()) {
    console.log('❌ Environment verification failed. Please fix and try again.')
    process.exit(1)
  }

  // Verify database setup
  await verifyDatabaseSetup()

  // Run API tests
  const tester = new AIBookingSystemTester()
  await tester.runAllTests()

  console.log('\n🎉 Test suite completed!')
  console.log('\n📝 Next Steps:')
  console.log('1. Fix any failed tests')
  console.log('2. Set up Twilio webhook for voice testing')
  console.log('3. Test with actual phone calls')
  console.log('4. Monitor logs during testing')
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error)
}

module.exports = { AIBookingSystemTester }
